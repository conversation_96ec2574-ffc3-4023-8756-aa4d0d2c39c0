#!/usr/bin/env python3
"""
Quick script to fix __import__('random') usage in discord_bot.py
This replaces all instances with proper random module usage.
"""

import re
import os

def fix_random_imports(file_path):
    """Fix random import issues in the Discord bot file"""
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    print(f"🔧 Fixing random imports in {file_path}")
    
    # Read the file
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Count occurrences before fixing
    before_count = len(re.findall(r"__import__\('random'\)", content))
    print(f"Found {before_count} instances of __import__('random')")
    
    if before_count == 0:
        print("✅ No random import issues found")
        return True
    
    # Replace all instances of __import__('random') with random
    content = re.sub(r"__import__\('random'\)", "random", content)
    
    # Count occurrences after fixing
    after_count = len(re.findall(r"__import__\('random'\)", content))
    fixed_count = before_count - after_count
    
    # Write the file back
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ Fixed {fixed_count} instances")
    
    if after_count > 0:
        print(f"⚠️ {after_count} instances still remain (manual review needed)")
    
    return True

def main():
    """Main function"""
    print("🚀 Random Import Fixer for Lil' Gargs Discord Bot")
    print("=" * 50)
    
    # Fix the main bot file
    bot_file = "backend/discord_bot.py"
    
    if fix_random_imports(bot_file):
        print("\n✅ Random import fixes completed!")
        print("\nNext steps:")
        print("1. Review the changes in your IDE")
        print("2. Test the bot locally: cd backend && python discord_bot.py")
        print("3. Deploy to Fly.io: ./deploy.sh")
    else:
        print("\n❌ Failed to fix random imports")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())

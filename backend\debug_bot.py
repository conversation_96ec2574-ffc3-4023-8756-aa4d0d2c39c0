#!/usr/bin/env python3
"""
Debug version of the Discord bot to identify startup issues
"""

import discord
from discord.ext import commands
import os
import asyncio
import logging
from datetime import datetime, timezone
from dotenv import load_dotenv
from motor.motor_asyncio import AsyncIOMotorClient
import google.generativeai as genai

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv('backend/.env')  # Specify the path to .env file

print("🔍 DEBUG: Starting bot initialization...")

class DebugBot(commands.Bot):
    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = True
        intents.guilds = True
        intents.members = True
        
        super().__init__(command_prefix='!', intents=intents)
        
        print("🔍 DEBUG: Bot class initialized")
        
        # Configuration
        self.start_time = datetime.now(timezone.utc)
        
        # API Keys
        self.gemini_api_key = os.getenv('GEMINI_API_KEY')
        self.mongo_url = os.getenv('MONGO_URL')
        self.db_name = os.getenv('DB_NAME', 'lil-gargs-cluster')
        
        print(f"🔍 DEBUG: API keys loaded - Gemini: {'✅' if self.gemini_api_key else '❌'}, MongoDB: {'✅' if self.mongo_url else '❌'}")
        
        # Initialize components
        self.mongo_client = None
        self.db = None
        self.ai_enabled = False

    async def setup_hook(self):
        """Called when the bot is starting up"""
        print("🔍 DEBUG: setup_hook called")
        
        try:
            # Test database connection
            print("🔍 DEBUG: Testing database connection...")
            self.mongo_client = AsyncIOMotorClient(self.mongo_url)
            await self.mongo_client.admin.command('ping')
            self.db = self.mongo_client[self.db_name]
            print("✅ DEBUG: Database connected successfully")
            
        except Exception as e:
            print(f"❌ DEBUG: Database connection failed: {e}")
        
        try:
            # Test Gemini API
            print("🔍 DEBUG: Testing Gemini API...")
            if self.gemini_api_key:
                genai.configure(api_key=self.gemini_api_key)
                model = genai.GenerativeModel("gemini-1.5-flash")
                test_response = model.generate_content("Hello")
                if test_response.text:
                    self.ai_enabled = True
                    print("✅ DEBUG: Gemini API connected successfully")
                else:
                    print("❌ DEBUG: Gemini API test failed - no response")
            else:
                print("❌ DEBUG: No Gemini API key found")
                
        except Exception as e:
            print(f"❌ DEBUG: Gemini API connection failed: {e}")
        
        try:
            # Sync commands
            print("🔍 DEBUG: Syncing commands...")
            synced = await self.tree.sync()
            print(f"✅ DEBUG: Synced {len(synced)} command(s)")
            
        except Exception as e:
            print(f"❌ DEBUG: Command sync failed: {e}")
        
        print("🔍 DEBUG: setup_hook completed")

    async def on_ready(self):
        """Called when the bot is ready"""
        print(f"🎉 DEBUG: Bot is ready!")
        print(f"   - Bot name: {self.user}")
        print(f"   - Bot ID: {self.user.id}")
        print(f"   - Guilds: {len(self.guilds)}")
        print(f"   - AI enabled: {self.ai_enabled}")
        print(f"   - Database: {'✅' if self.db else '❌'}")

# Create bot instance
bot = DebugBot()

# Add a simple test command
@bot.tree.command(name="test", description="Test command")
async def test_command(interaction: discord.Interaction):
    """Simple test command"""
    await interaction.response.send_message("✅ Bot is working!")

# Add system health command
@bot.tree.command(name="debug", description="Debug information")
async def debug_command(interaction: discord.Interaction):
    """Debug information command"""
    embed = discord.Embed(title="🔍 Debug Information", color=0x0099FF)
    embed.add_field(name="AI Enabled", value="✅" if bot.ai_enabled else "❌", inline=True)
    embed.add_field(name="Database", value="✅" if bot.db else "❌", inline=True)
    embed.add_field(name="Guilds", value=len(bot.guilds), inline=True)
    await interaction.response.send_message(embed=embed)

if __name__ == "__main__":
    print("🔍 DEBUG: Starting bot...")
    
    # Check token
    token = os.getenv('DISCORD_BOT_TOKEN')
    if not token:
        print("❌ DEBUG: No Discord token found!")
        exit(1)
    
    print(f"🔍 DEBUG: Token found (length: {len(token)})")
    
    try:
        bot.run(token)
    except Exception as e:
        print(f"❌ DEBUG: Bot failed to start: {e}")

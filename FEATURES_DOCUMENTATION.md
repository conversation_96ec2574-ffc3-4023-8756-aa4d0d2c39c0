# Lil Gargs Bot - New Features Documentation

## 🚀 Overview

This document covers the two major new features added to the Lil Gargs Discord bot:

1. **Enhanced NFT Verification System** - Verify Solana NFT ownership with automatic role assignment
2. **Document-Based AI Chat Integration** - Upload documents and ask AI questions about your project

---

## 🖼️ NFT Verification System

### Features
- **Solana Blockchain Integration**: Verify ownership of Lil Gargs NFTs using Helius API
- **Automatic Role Assignment**: Configure roles based on NFT holding thresholds
- **Enhanced User Experience**: Better error handling and informative responses
- **Wallet Validation**: Validates Solana wallet address format before verification

### Commands

#### `/nft verify [wallet_address]`
Verify your Solana wallet for Lil Gargs NFTs.

**Example:**
```
/nft verify DYw8jCTfwHNRJhhmFcbXvVDTqWMEVFBX6ZKUmG5CNSKK
```

**Features:**
- Shows verification progress
- Displays found NFTs (up to 5)
- Automatically assigns configured roles
- Provides helpful error messages for invalid addresses

#### `/nft flex [token_id]` (Existing)
Display a specific NFT from your collection.

#### `/nft stats` (Existing)
Show server-wide NFT holder statistics.

### Admin Configuration

#### `/config nft-role-add [role] [threshold]`
Add a role that gets automatically assigned based on NFT count.

**Example:**
```
/config nft-role-add @Holder 1
/config nft-role-add @Whale 10
```

#### `/config nft-role-remove [role]`
Remove a role from automatic assignment.

#### `/config view-config`
View current server configuration.

---

## 🤖 Document-Based AI Chat System

### Features
- **Document Upload**: Support for PDF, DOCX, and TXT files
- **AI-Powered Q&A**: Ask questions about uploaded documents using OpenAI GPT-4o-mini
- **Smart Chunking**: Documents are intelligently split for optimal AI processing
- **Source Attribution**: AI responses include source document references
- **Permission Control**: Configure who can upload documents

### Commands

#### `/docs upload [file]`
Upload a document to the AI knowledge base.

**Supported formats:** PDF, DOCX, TXT (max 10MB)

**Example:**
```
/docs upload (attach your project whitepaper.pdf)
```

**Features:**
- Automatic text extraction and processing
- Generates embeddings for semantic search
- Provides document ID for management
- Shows processing statistics

#### `/docs list`
View all uploaded documents in the server.

**Shows:**
- Document filename and type
- Upload date and uploader
- Document ID for management
- Number of text chunks created

#### `/docs delete [document_id]`
Delete a document from the knowledge base (Admin only).

#### `/docs summary [document_id]`
Generate an AI summary of a specific document.

#### `/ask [question]`
Ask questions about uploaded documents.

**Example:**
```
/ask What are the tokenomics of the project?
/ask How does the staking system work?
/ask What are the roadmap milestones?
```

**Features:**
- Searches through all uploaded documents
- Provides contextual answers based on document content
- Shows source documents used for the answer
- Handles cases where no relevant information is found

### Admin Configuration

#### `/config document-upload-role [role]`
Set which role can upload documents (default: Admin only).

**Example:**
```
/config document-upload-role @Moderator
```

---

## 🛠️ Setup Instructions

### 1. Environment Variables

Copy `backend/.env.example` to `backend/.env` and configure:

```env
# Required for NFT verification
HELIUS_API_KEY=your_helius_api_key_here
VERIFIED_CREATOR=9fT6Spqbv9FxK7Ktxr6bDfASWc6k5acUNr1zMv5WrGfA
NFT_CONTRACT_ADDRESS=FP2bGBGHWrW4w82hsSDGc5zNLQ83CvEmW2shGkttS7aZ

# Required for document-based AI
OPENAI_API_KEY=your_openai_api_key_here
```

### 2. API Keys Setup

#### Helius API (NFT Verification)
1. Go to [helius.xyz](https://helius.xyz/)
2. Create account and get API key
3. Free tier: 100 requests/day

#### OpenAI API (Document AI)
1. Go to [platform.openai.com](https://platform.openai.com/api-keys)
2. Create API key
3. Uses GPT-4o-mini (cost-effective model)

### 3. Dependencies

All required dependencies are already in `requirements.txt`:
- `openai>=1.0.0`
- `sentence-transformers>=2.2.0`
- `PyPDF2>=3.0.0`
- `python-docx>=1.1.0`
- `solders>=0.18.0`

### 4. Database Collections

The bot automatically creates these new collections:
- `documents` - Document metadata
- `document_chunks` - Text chunks with embeddings
- `qa_interactions` - Q&A history for analytics

---

## 💰 Cost Estimates

### OpenAI API (Document Q&A)
- **GPT-4o-mini**: ~$0.15 per 1M input tokens, ~$0.60 per 1M output tokens
- **Embeddings**: ~$0.02 per 1M tokens
- **Estimated**: $1-5/month for small-medium communities

### Helius API (NFT Verification)
- **Free tier**: 100 requests/day
- **Paid tiers**: Start at $9/month

---

## 🔧 Troubleshooting

### NFT Verification Issues
- **Invalid wallet address**: Ensure using Solana wallet address (32-44 characters)
- **No NFTs found**: Double-check wallet address and NFT ownership
- **Role assignment failed**: Check bot permissions and role hierarchy

### Document Upload Issues
- **File too large**: Max 10MB file size
- **Unsupported format**: Use PDF, DOCX, or TXT files
- **Permission denied**: Check document upload role configuration

### AI Response Issues
- **No documents found**: Upload documents first using `/docs upload`
- **No relevant information**: Try rephrasing question or check document content
- **API errors**: Verify OpenAI API key and account credits

---

## 📊 Usage Analytics

The bot tracks:
- NFT verification attempts and success rates
- Document upload statistics
- Q&A interaction history
- Popular questions for insights

Access analytics through the database `qa_interactions` collection.

# 🚀 Lil' Gargs Discord Bot Deployment Guide

## 📋 Prerequisites

Before deploying, ensure you have:
- Python 3.8+ installed
- MongoDB database (local or cloud)
- Discord Bot Token
- Google Gemini API Key
- Helius API Key (for NFT verification)

## 🔧 Step 1: Environment Setup

### 1.1 Install Dependencies

```bash
# Navigate to your project directory
cd lil-gargs-bot

# Install Python dependencies
pip install -r backend/requirements.txt
```

### 1.2 Configure Environment Variables

Update your `backend/.env` file with all required keys:

```env
# Database
MONGO_URL="mongodb://localhost:27017"
DB_NAME="lil_gargs_bot"

# Discord Bot Configuration
DISCORD_BOT_TOKEN="your_discord_bot_token_here"

# AI Configuration  
GEMINI_API_KEY="your_google_gemini_api_key_here"

# Solana/NFT Configuration
HELIUS_API_KEY="your_helius_api_key_here"
NFT_CONTRACT_ADDRESS="FP2bGBGHWrW4w82hsSDGc5zNLQ83CvEmW2shGkttS7aZ"
VERIFIED_CREATOR="9fT6Spqbv9FxK7Ktxr6bDfASWc6k5acUNr1zMv5WrGfA"
IPFS_IMAGE_FOLDER="https://bafybeif32gaqsngxdaply6x5m5htxpuuxw2dljvdv6iokek3xod7lmus24.ipfs.w3s.link/"
IPFS_JSON_FOLDER="https://bafybeibelmc3mnauyxzhez3g5wbxmz4n6ibme7hxvkpryqwlj4bha6jeva.ipfs.w3s.link/"

# OpenAI (optional, for document features)
OPENAI_API_KEY="your_openai_api_key_here"
```

## 🔑 Step 2: Get Required API Keys

### 2.1 Discord Bot Token
1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Create a new application or select existing one
3. Go to "Bot" section
4. Copy the bot token
5. **Important**: Keep this token secret!

### 2.2 Google Gemini API Key
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with Google account
3. Click "Create API Key"
4. Copy the generated key

### 2.3 Helius API Key
1. Go to [Helius](https://helius.xyz/)
2. Sign up for an account
3. Create a new project
4. Copy your API key

## 🗄️ Step 3: Database Setup

### Option A: Local MongoDB
```bash
# Install MongoDB locally
# Windows: Download from https://www.mongodb.com/try/download/community
# macOS: brew install mongodb-community
# Linux: Follow MongoDB installation guide

# Start MongoDB service
mongod
```

### Option B: MongoDB Atlas (Cloud)
1. Go to [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create free cluster
3. Get connection string
4. Update `MONGO_URL` in `.env`

## 🤖 Step 4: Discord Bot Setup

### 4.1 Bot Permissions
Your bot needs these permissions:
- Send Messages
- Use Slash Commands
- Read Message History
- Embed Links
- Attach Files
- Manage Roles (for NFT role assignment)
- Create Private Threads (for ticket system)

### 4.2 Invite Bot to Server
1. Go to Discord Developer Portal → Your App → OAuth2 → URL Generator
2. Select "bot" and "applications.commands" scopes
3. Select required permissions
4. Use generated URL to invite bot

## 🚀 Step 5: Deploy and Test

### 5.1 Run the Bot

```bash
# Navigate to backend directory
cd backend

# Run the bot
python discord_bot.py
```

### 5.2 Check Startup Logs

Look for these success messages:
```
✅ Gemini API connection successful
✅ Database connected
✅ Synced X command(s)
Bot has connected to Discord!
```

### 5.3 Test Basic Functionality

Run these commands in your Discord server:

```bash
# Test system health
/systemhealth

# Test AI functionality
/askgarg What are Lil' Gargs?

# Test NFT verification (use a real Solana wallet)
/nft verify DYw8jCTfwHNRJhhmFcbXvVDTqWMEVFBX6ZKUmG5CNSKK

# Check usage statistics
/aistats
```

## 🧪 Step 6: Comprehensive Testing

### 6.1 Run Automated Tests

```bash
# Run the test suite
python run_tests.py
```

### 6.2 Manual Testing Checklist

#### AI Features ✅
- [ ] `/askgarg <question>` - AI responds appropriately
- [ ] `/suggest` - Generates discussion prompts
- [ ] `/gargoracle <question>` - Mystical responses
- [ ] `@bot <message>` - Responds to mentions
- [ ] Rate limiting works (try 51+ requests)

#### NFT Verification ✅
- [ ] `/nft verify <valid_wallet>` - Shows NFT count
- [ ] `/nft verify <invalid_wallet>` - Shows error
- [ ] Rate limiting works (try 11+ verifications)
- [ ] Security messages displayed

#### Admin Features ✅
- [ ] `/aidocs set <content>` - Sets custom documentation
- [ ] `/aidocs view` - Shows current documentation
- [ ] `/systemhealth` - Shows service status
- [ ] `/aistats` - Shows usage statistics

#### Error Handling ✅
- [ ] Invalid commands show helpful errors
- [ ] API failures are handled gracefully
- [ ] Rate limits show clear messages

## 🔧 Step 7: Production Deployment

### Option A: VPS/Dedicated Server

```bash
# Install PM2 for process management
npm install -g pm2

# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'lil-gargs-bot',
    script: 'python',
    args: 'discord_bot.py',
    cwd: './backend',
    interpreter: 'none',
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production'
    }
  }]
}
EOF

# Start with PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### Option B: Docker Deployment

```dockerfile
# Create Dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY backend/requirements.txt .
RUN pip install -r requirements.txt

COPY backend/ .
CMD ["python", "discord_bot.py"]
```

```bash
# Build and run
docker build -t lil-gargs-bot .
docker run -d --env-file backend/.env lil-gargs-bot
```

### Option C: Cloud Platforms

#### Railway
1. Connect GitHub repository
2. Add environment variables
3. Deploy automatically

#### Heroku
```bash
# Install Heroku CLI
# Create Procfile
echo "worker: python backend/discord_bot.py" > Procfile

# Deploy
heroku create your-bot-name
heroku config:set DISCORD_BOT_TOKEN=your_token
# ... set other env vars
git push heroku main
```

## 📊 Step 8: Monitoring and Maintenance

### 8.1 Monitor Bot Health

```bash
# Check system status regularly
/systemhealth

# Monitor usage patterns
/aistats

# Check error logs in database
```

### 8.2 Regular Maintenance

- **Weekly**: Check `/systemhealth` for any issues
- **Monthly**: Review error logs and usage statistics
- **As needed**: Update API keys if they expire
- **Updates**: Keep dependencies updated

## 🚨 Troubleshooting

### Common Issues

#### Bot Won't Start
```bash
# Check logs for specific errors
python discord_bot.py

# Common fixes:
# 1. Verify all environment variables are set
# 2. Check MongoDB is running
# 3. Verify API keys are valid
# 4. Ensure Python dependencies are installed
```

#### Commands Not Working
```bash
# Check if commands are synced
# Look for "Synced X command(s)" in logs

# If not synced, restart bot
# Commands may take up to 1 hour to appear globally
```

#### AI Not Responding
```bash
# Check Gemini API status
/systemhealth

# Verify API key in .env file
# Check rate limits with /aistats
```

#### NFT Verification Failing
```bash
# Check Helius API status
/systemhealth

# Verify Helius API key
# Test with known valid wallet address
```

## 📞 Support

If you encounter issues:

1. **Check logs**: Look for error messages in console output
2. **Use health check**: `/systemhealth` shows service status
3. **Review documentation**: Check relevant .md files
4. **Test step by step**: Use the testing checklist above

## ✅ Deployment Checklist

- [ ] All dependencies installed (`pip install -r backend/requirements.txt`)
- [ ] Environment variables configured in `backend/.env`
- [ ] MongoDB running and accessible
- [ ] Discord bot invited to server with proper permissions
- [ ] All API keys obtained and configured
- [ ] Bot starts without errors
- [ ] System health check passes (`/systemhealth`)
- [ ] Basic commands work (`/askgarg`, `/nft verify`)
- [ ] Rate limiting tested and working
- [ ] Production deployment configured (PM2/Docker/Cloud)
- [ ] Monitoring setup for ongoing maintenance

## 🎉 Success!

Once all items are checked, your enhanced Lil' Gargs Discord bot is ready for production use! 

Your bot now features:
- ✅ Independent AI (no Emergent.ai dependency)
- ✅ Secure NFT verification
- ✅ Comprehensive rate limiting
- ✅ Admin tools and monitoring
- ✅ Robust error handling

Enjoy your fully functional Discord bot! 🤖✨

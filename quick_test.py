#!/usr/bin/env python3
"""
Quick test script to verify bot configuration and basic functionality.
Run this before deploying to catch common issues early.
"""

import os
import sys
import asyncio
import aiohttp
from datetime import datetime

def check_file_exists(filepath, description):
    """Check if a required file exists"""
    if os.path.exists(filepath):
        print(f"✅ {description}: {filepath}")
        return True
    else:
        print(f"❌ {description} missing: {filepath}")
        return False

def check_env_var(var_name, required=True):
    """Check if environment variable is set"""
    value = os.getenv(var_name)
    if value:
        # Mask sensitive values
        if 'KEY' in var_name or 'TOKEN' in var_name:
            masked_value = value[:8] + "..." + value[-4:] if len(value) > 12 else "***"
            print(f"✅ {var_name}: {masked_value}")
        else:
            print(f"✅ {var_name}: {value}")
        return True
    else:
        status = "❌" if required else "⚠️"
        req_text = "required" if required else "optional"
        print(f"{status} {var_name}: Not set ({req_text})")
        return not required

async def test_api_connection(url, name, headers=None):
    """Test API connection"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers, timeout=10) as response:
                if response.status == 200:
                    print(f"✅ {name}: Connection successful")
                    return True
                else:
                    print(f"❌ {name}: HTTP {response.status}")
                    return False
    except asyncio.TimeoutError:
        print(f"❌ {name}: Connection timeout")
        return False
    except Exception as e:
        print(f"❌ {name}: {str(e)}")
        return False

async def test_gemini_api(api_key):
    """Test Google Gemini API"""
    try:
        import google.generativeai as genai
        genai.configure(api_key=api_key)
        
        model = genai.GenerativeModel("gemini-1.5-flash")
        response = model.generate_content("Say 'API test successful'", 
                                        generation_config={"max_output_tokens": 10})
        
        if response.text and "successful" in response.text.lower():
            print("✅ Gemini API: Connection and response successful")
            return True
        else:
            print("❌ Gemini API: Unexpected response")
            return False
    except Exception as e:
        print(f"❌ Gemini API: {str(e)}")
        return False

async def test_helius_api(api_key):
    """Test Helius API"""
    url = f"https://mainnet.helius-rpc.com/?api-key={api_key}"
    payload = {
        "jsonrpc": "2.0",
        "id": "test",
        "method": "getAssetsByOwner",
        "params": {
            "ownerAddress": "********************************",
            "page": 1,
            "limit": 1
        }
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()
                    if 'result' in data:
                        print("✅ Helius API: Connection successful")
                        return True
                    else:
                        print("❌ Helius API: Invalid response format")
                        return False
                else:
                    print(f"❌ Helius API: HTTP {response.status}")
                    return False
    except Exception as e:
        print(f"❌ Helius API: {str(e)}")
        return False

def check_python_dependencies():
    """Check if required Python packages are installed"""
    required_packages = [
        'discord.py',
        'motor',
        'aiohttp',
        'google.generativeai',
        'pymongo',
        'python-dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_').replace('.', '_'))
            print(f"✅ {package}: Installed")
        except ImportError:
            print(f"❌ {package}: Not installed")
            missing_packages.append(package)
    
    return len(missing_packages) == 0, missing_packages

async def test_mongodb_connection(mongo_url):
    """Test MongoDB connection"""
    try:
        from motor.motor_asyncio import AsyncIOMotorClient
        client = AsyncIOMotorClient(mongo_url)
        
        # Test connection
        await client.admin.command('ping')
        print("✅ MongoDB: Connection successful")
        
        # Test database access
        db = client[os.getenv('DB_NAME', 'lil_gargs_bot')]
        collections = await db.list_collection_names()
        print(f"✅ MongoDB: Database accessible ({len(collections)} collections)")
        
        client.close()
        return True
    except Exception as e:
        print(f"❌ MongoDB: {str(e)}")
        return False

async def main():
    """Main testing function"""
    print("🧪 Lil' Gargs Discord Bot - Quick Configuration Test")
    print("=" * 60)
    
    # Load environment variables
    from dotenv import load_dotenv
    env_path = os.path.join('backend', '.env')
    if os.path.exists(env_path):
        load_dotenv(env_path)
        print(f"✅ Loaded environment from: {env_path}")
    else:
        print(f"⚠️ No .env file found at: {env_path}")
    
    print("\n📁 File Structure Check")
    print("-" * 30)
    
    files_ok = True
    files_ok &= check_file_exists("backend/discord_bot.py", "Main bot file")
    files_ok &= check_file_exists("backend/requirements.txt", "Requirements file")
    files_ok &= check_file_exists("backend/.env", "Environment file")
    
    print("\n🔧 Python Dependencies Check")
    print("-" * 30)
    
    deps_ok, missing = check_python_dependencies()
    if not deps_ok:
        print(f"\n❌ Missing packages: {', '.join(missing)}")
        print("Install with: pip install " + " ".join(missing))
    
    print("\n🔑 Environment Variables Check")
    print("-" * 30)
    
    env_ok = True
    env_ok &= check_env_var("DISCORD_BOT_TOKEN", required=True)
    env_ok &= check_env_var("GEMINI_API_KEY", required=True)
    env_ok &= check_env_var("HELIUS_API_KEY", required=True)
    env_ok &= check_env_var("MONGO_URL", required=True)
    env_ok &= check_env_var("DB_NAME", required=True)
    env_ok &= check_env_var("NFT_CONTRACT_ADDRESS", required=True)
    env_ok &= check_env_var("VERIFIED_CREATOR", required=True)
    env_ok &= check_env_var("OPENAI_API_KEY", required=False)
    
    print("\n🌐 API Connections Test")
    print("-" * 30)
    
    api_tests = []
    
    # Test MongoDB
    mongo_url = os.getenv("MONGO_URL")
    if mongo_url:
        api_tests.append(await test_mongodb_connection(mongo_url))
    else:
        print("❌ MongoDB: No MONGO_URL configured")
        api_tests.append(False)
    
    # Test Gemini API
    gemini_key = os.getenv("GEMINI_API_KEY")
    if gemini_key:
        api_tests.append(await test_gemini_api(gemini_key))
    else:
        print("❌ Gemini API: No API key configured")
        api_tests.append(False)
    
    # Test Helius API
    helius_key = os.getenv("HELIUS_API_KEY")
    if helius_key:
        api_tests.append(await test_helius_api(helius_key))
    else:
        print("❌ Helius API: No API key configured")
        api_tests.append(False)
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 60)
    
    total_tests = 4  # Files, Dependencies, Environment, APIs
    passed_tests = sum([
        files_ok,
        deps_ok,
        env_ok,
        all(api_tests)
    ])
    
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("\n🎉 All tests passed! Your bot is ready to deploy.")
        print("\nNext steps:")
        print("1. Run: cd backend && python discord_bot.py")
        print("2. Check for '✅ Gemini API connection successful' in logs")
        print("3. Test commands in Discord: /systemhealth, /askgarg")
    else:
        print("\n❌ Some tests failed. Please fix the issues above before deploying.")
        print("\nCommon fixes:")
        if not files_ok:
            print("- Ensure you're in the correct directory")
        if not deps_ok:
            print("- Run: pip install -r backend/requirements.txt")
        if not env_ok:
            print("- Update backend/.env with missing variables")
        if not all(api_tests):
            print("- Verify API keys are correct and services are accessible")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Test failed with error: {e}")
        sys.exit(1)

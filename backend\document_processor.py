"""
Document Processing Module for Lil Gargs Bot
Handles document upload, text extraction, chunking, and embedding generation
"""

import os
import io
import uuid
import hashlib
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import asyncio
import aiohttp

# Document processing imports
import PyPDF2
import docx
import magic
import tiktoken

# AI and embedding imports
import openai
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np

# Database
from motor.motor_asyncio import AsyncIOMotorClient

logger = logging.getLogger(__name__)

class DocumentProcessor:
    """Handles document processing, embedding generation, and retrieval"""
    
    def __init__(self, openai_api_key: str, mongo_client: AsyncIOMotorClient, db_name: str):
        self.openai_client = openai.AsyncOpenAI(api_key=openai_api_key)
        self.mongo_client = mongo_client
        self.db = mongo_client[db_name]
        
        # Initialize sentence transformer for embeddings
        self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
        
        # Initialize tokenizer for chunk size management
        self.tokenizer = tiktoken.get_encoding("cl100k_base")
        
        # Configuration
        self.max_chunk_size = 1000  # tokens
        self.chunk_overlap = 200    # tokens
        self.max_file_size = 10 * 1024 * 1024  # 10MB
        
    async def process_document(self, file_data: bytes, filename: str, uploaded_by: str, guild_id: str) -> Dict[str, Any]:
        """Process uploaded document and store in database"""
        try:
            # Validate file size
            if len(file_data) > self.max_file_size:
                return {'success': False, 'error': 'File too large (max 10MB)'}
            
            # Detect file type
            file_type = self._detect_file_type(file_data, filename)
            if not file_type:
                return {'success': False, 'error': 'Unsupported file type'}
            
            # Extract text from document
            text_content = await self._extract_text(file_data, file_type)
            if not text_content:
                return {'success': False, 'error': 'Could not extract text from document'}
            
            # Generate document hash for deduplication
            doc_hash = hashlib.sha256(file_data).hexdigest()
            
            # Check if document already exists
            existing_doc = await self.db.documents.find_one({"document_hash": doc_hash})
            if existing_doc:
                return {'success': False, 'error': 'Document already exists in database'}
            
            # Create document chunks
            chunks = self._create_chunks(text_content)
            
            # Generate embeddings for chunks
            chunk_embeddings = await self._generate_embeddings(chunks)
            
            # Create document record
            document_id = str(uuid.uuid4())
            document_record = {
                "document_id": document_id,
                "filename": filename,
                "file_type": file_type,
                "document_hash": doc_hash,
                "uploaded_by": uploaded_by,
                "guild_id": guild_id,
                "upload_date": datetime.utcnow(),
                "text_content": text_content,
                "chunk_count": len(chunks),
                "file_size": len(file_data),
                "status": "processed"
            }
            
            # Store document
            await self.db.documents.insert_one(document_record)
            
            # Store chunks with embeddings
            chunk_records = []
            for i, (chunk_text, embedding) in enumerate(zip(chunks, chunk_embeddings)):
                chunk_record = {
                    "chunk_id": str(uuid.uuid4()),
                    "document_id": document_id,
                    "chunk_index": i,
                    "text": chunk_text,
                    "embedding": embedding.tolist(),  # Convert numpy array to list
                    "token_count": len(self.tokenizer.encode(chunk_text)),
                    "created_at": datetime.utcnow()
                }
                chunk_records.append(chunk_record)
            
            if chunk_records:
                await self.db.document_chunks.insert_many(chunk_records)
            
            logger.info(f"Successfully processed document: {filename} ({len(chunks)} chunks)")
            
            return {
                'success': True,
                'document_id': document_id,
                'filename': filename,
                'chunk_count': len(chunks),
                'text_length': len(text_content)
            }
            
        except Exception as e:
            logger.error(f"Error processing document {filename}: {e}")
            return {'success': False, 'error': f'Processing failed: {str(e)}'}
    
    def _detect_file_type(self, file_data: bytes, filename: str) -> Optional[str]:
        """Detect file type from data and filename"""
        try:
            # Use python-magic to detect MIME type
            mime_type = magic.from_buffer(file_data, mime=True)
            
            # Map MIME types to our supported types
            type_mapping = {
                'application/pdf': 'pdf',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
                'application/msword': 'doc',
                'text/plain': 'txt'
            }
            
            detected_type = type_mapping.get(mime_type)
            
            # Fallback to file extension
            if not detected_type:
                ext = filename.lower().split('.')[-1] if '.' in filename else ''
                ext_mapping = {
                    'pdf': 'pdf',
                    'docx': 'docx',
                    'doc': 'doc',
                    'txt': 'txt'
                }
                detected_type = ext_mapping.get(ext)
            
            return detected_type
            
        except Exception as e:
            logger.error(f"Error detecting file type: {e}")
            return None
    
    async def _extract_text(self, file_data: bytes, file_type: str) -> Optional[str]:
        """Extract text content from different file types"""
        try:
            if file_type == 'pdf':
                return self._extract_pdf_text(file_data)
            elif file_type == 'docx':
                return self._extract_docx_text(file_data)
            elif file_type == 'txt':
                return file_data.decode('utf-8', errors='ignore')
            else:
                return None
                
        except Exception as e:
            logger.error(f"Error extracting text from {file_type}: {e}")
            return None
    
    def _extract_pdf_text(self, file_data: bytes) -> str:
        """Extract text from PDF file"""
        text_content = []
        pdf_file = io.BytesIO(file_data)
        pdf_reader = PyPDF2.PdfReader(pdf_file)
        
        for page in pdf_reader.pages:
            text_content.append(page.extract_text())
        
        return '\n'.join(text_content)
    
    def _extract_docx_text(self, file_data: bytes) -> str:
        """Extract text from DOCX file"""
        doc_file = io.BytesIO(file_data)
        doc = docx.Document(doc_file)
        
        text_content = []
        for paragraph in doc.paragraphs:
            text_content.append(paragraph.text)
        
        return '\n'.join(text_content)
    
    def _create_chunks(self, text: str) -> List[str]:
        """Split text into overlapping chunks"""
        # Tokenize the text
        tokens = self.tokenizer.encode(text)
        
        chunks = []
        start = 0
        
        while start < len(tokens):
            # Calculate end position
            end = min(start + self.max_chunk_size, len(tokens))
            
            # Extract chunk tokens
            chunk_tokens = tokens[start:end]
            
            # Decode back to text
            chunk_text = self.tokenizer.decode(chunk_tokens)
            chunks.append(chunk_text.strip())
            
            # Move start position with overlap
            if end == len(tokens):
                break
            start = end - self.chunk_overlap
        
        return chunks
    
    async def _generate_embeddings(self, texts: List[str]) -> List[np.ndarray]:
        """Generate embeddings for text chunks"""
        try:
            # Use sentence transformer to generate embeddings
            embeddings = self.embedding_model.encode(texts)
            return embeddings
            
        except Exception as e:
            logger.error(f"Error generating embeddings: {e}")
            return []
    
    async def search_documents(self, query: str, guild_id: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search for relevant document chunks based on query"""
        try:
            # Generate query embedding
            query_embedding = self.embedding_model.encode([query])[0]
            
            # Get all chunks for the guild
            chunks_cursor = self.db.document_chunks.aggregate([
                {
                    "$lookup": {
                        "from": "documents",
                        "localField": "document_id",
                        "foreignField": "document_id",
                        "as": "document"
                    }
                },
                {
                    "$match": {
                        "document.guild_id": guild_id
                    }
                }
            ])
            
            chunks = await chunks_cursor.to_list(length=None)
            
            if not chunks:
                return []
            
            # Calculate similarities
            similarities = []
            for chunk in chunks:
                chunk_embedding = np.array(chunk['embedding'])
                similarity = cosine_similarity([query_embedding], [chunk_embedding])[0][0]
                similarities.append((similarity, chunk))
            
            # Sort by similarity and return top_k
            similarities.sort(key=lambda x: x[0], reverse=True)
            
            results = []
            for similarity, chunk in similarities[:top_k]:
                results.append({
                    'chunk_id': chunk['chunk_id'],
                    'document_id': chunk['document_id'],
                    'text': chunk['text'],
                    'similarity': float(similarity),
                    'filename': chunk['document'][0]['filename'] if chunk['document'] else 'Unknown',
                    'chunk_index': chunk['chunk_index']
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching documents: {e}")
            return []
    
    async def get_document_list(self, guild_id: str) -> List[Dict[str, Any]]:
        """Get list of uploaded documents for a guild"""
        try:
            documents = await self.db.documents.find(
                {"guild_id": guild_id},
                {
                    "document_id": 1,
                    "filename": 1,
                    "file_type": 1,
                    "upload_date": 1,
                    "uploaded_by": 1,
                    "chunk_count": 1,
                    "file_size": 1
                }
            ).to_list(length=None)
            
            return documents
            
        except Exception as e:
            logger.error(f"Error getting document list: {e}")
            return []
    
    async def delete_document(self, document_id: str, guild_id: str) -> bool:
        """Delete a document and its chunks"""
        try:
            # Verify document belongs to guild
            document = await self.db.documents.find_one({
                "document_id": document_id,
                "guild_id": guild_id
            })
            
            if not document:
                return False
            
            # Delete chunks
            await self.db.document_chunks.delete_many({"document_id": document_id})
            
            # Delete document
            await self.db.documents.delete_one({"document_id": document_id})
            
            logger.info(f"Deleted document: {document_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting document {document_id}: {e}")
            return False

#!/usr/bin/env python3
"""
Test script to verify Lil <PERSON>ar<PERSON> setup and dependencies
Run this script to check if all required components are properly configured
"""

import os
import sys
import asyncio
from pathlib import Path

# Add backend to path
sys.path.append(str(Path(__file__).parent / "backend"))

def test_imports():
    """Test if all required modules can be imported"""
    print("🔍 Testing imports...")
    
    try:
        import discord
        print("✅ discord.py imported successfully")
    except ImportError as e:
        print(f"❌ discord.py import failed: {e}")
        return False
    
    try:
        import openai
        print("✅ openai imported successfully")
    except ImportError as e:
        print(f"❌ openai import failed: {e}")
        return False
    
    try:
        from sentence_transformers import SentenceTransformer
        print("✅ sentence-transformers imported successfully")
    except ImportError as e:
        print(f"❌ sentence-transformers import failed: {e}")
        return False
    
    try:
        import PyPDF2
        print("✅ PyPDF2 imported successfully")
    except ImportError as e:
        print(f"❌ PyPDF2 import failed: {e}")
        return False
    
    try:
        import docx
        print("✅ python-docx imported successfully")
    except ImportError as e:
        print(f"❌ python-docx import failed: {e}")
        return False
    
    try:
        import base58
        print("✅ base58 imported successfully")
    except ImportError as e:
        print(f"❌ base58 import failed: {e}")
        return False
    
    try:
        from motor.motor_asyncio import AsyncIOMotorClient
        print("✅ motor (MongoDB) imported successfully")
    except ImportError as e:
        print(f"❌ motor import failed: {e}")
        return False
    
    return True

def test_environment():
    """Test environment variables"""
    print("\n🔍 Testing environment variables...")
    
    required_vars = [
        "DISCORD_BOT_TOKEN",
        "MONGO_URL", 
        "DB_NAME"
    ]
    
    optional_vars = [
        "OPENAI_API_KEY",
        "HELIUS_API_KEY", 
        "GEMINI_API_KEY",
        "VERIFIED_CREATOR",
        "NFT_CONTRACT_ADDRESS"
    ]
    
    missing_required = []
    missing_optional = []
    
    for var in required_vars:
        if os.getenv(var):
            print(f"✅ {var} is set")
        else:
            print(f"❌ {var} is missing (REQUIRED)")
            missing_required.append(var)
    
    for var in optional_vars:
        if os.getenv(var):
            print(f"✅ {var} is set")
        else:
            print(f"⚠️  {var} is missing (optional feature will be disabled)")
            missing_optional.append(var)
    
    return len(missing_required) == 0, missing_required, missing_optional

async def test_document_processor():
    """Test document processor initialization"""
    print("\n🔍 Testing document processor...")
    
    try:
        from document_processor import DocumentProcessor
        from motor.motor_asyncio import AsyncIOMotorClient
        
        openai_key = os.getenv('OPENAI_API_KEY')
        mongo_url = os.getenv('MONGO_URL', 'mongodb://localhost:27017')
        db_name = os.getenv('DB_NAME', 'test_db')
        
        if not openai_key:
            print("⚠️  OpenAI API key not set - document processor test skipped")
            return True
        
        client = AsyncIOMotorClient(mongo_url)
        processor = DocumentProcessor(openai_key, client, db_name)
        print("✅ Document processor initialized successfully")
        
        # Test embedding model
        test_texts = ["This is a test document."]
        embeddings = await processor._generate_embeddings(test_texts)
        if len(embeddings) > 0:
            print("✅ Embedding generation working")
        else:
            print("❌ Embedding generation failed")
            return False
        
        await client.close()
        return True
        
    except Exception as e:
        print(f"❌ Document processor test failed: {e}")
        return False

async def test_bot_modules():
    """Test bot module imports"""
    print("\n🔍 Testing bot modules...")
    
    try:
        from discord_bot import LilGargsBot
        print("✅ Bot class imported successfully")
        
        # Test wallet validation
        bot = LilGargsBot()
        
        # Test valid address
        valid_address = "DYw8jCTfwHNRJhhmFcbXvVDTqWMEVFBX6ZKUmG5CNSKK"
        if bot._is_valid_solana_address(valid_address):
            print("✅ Wallet validation working")
        else:
            print("❌ Wallet validation failed")
            return False
        
        # Test invalid address
        invalid_address = "invalid_address"
        if not bot._is_valid_solana_address(invalid_address):
            print("✅ Invalid wallet detection working")
        else:
            print("❌ Invalid wallet detection failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Bot module test failed: {e}")
        return False

def print_setup_summary(env_ok, missing_required, missing_optional):
    """Print setup summary and next steps"""
    print("\n" + "="*50)
    print("📋 SETUP SUMMARY")
    print("="*50)
    
    if env_ok:
        print("✅ All required environment variables are set")
    else:
        print("❌ Missing required environment variables:")
        for var in missing_required:
            print(f"   - {var}")
    
    if missing_optional:
        print("\n⚠️  Optional features disabled due to missing variables:")
        for var in missing_optional:
            feature = {
                'OPENAI_API_KEY': 'Document-based AI chat',
                'HELIUS_API_KEY': 'NFT verification', 
                'GEMINI_API_KEY': 'General AI chat',
                'VERIFIED_CREATOR': 'NFT verification',
                'NFT_CONTRACT_ADDRESS': 'NFT verification'
            }.get(var, 'Unknown feature')
            print(f"   - {var} → {feature}")
    
    print("\n📝 NEXT STEPS:")
    if not env_ok:
        print("1. Copy backend/.env.example to backend/.env")
        print("2. Fill in the missing required environment variables")
        print("3. Run this test script again")
    else:
        print("1. Start your MongoDB server")
        print("2. Run the bot: python backend/discord_bot.py")
        print("3. Invite the bot to your Discord server")
        print("4. Test the new features!")
    
    print("\n📚 Documentation:")
    print("- See FEATURES_DOCUMENTATION.md for detailed usage instructions")
    print("- See backend/.env.example for environment variable setup")

async def main():
    """Run all tests"""
    print("🤖 Lil Gargs Bot Setup Test")
    print("="*50)
    
    # Test imports
    imports_ok = test_imports()
    
    # Test environment
    env_ok, missing_required, missing_optional = test_environment()
    
    # Test document processor (if OpenAI key available)
    if imports_ok and os.getenv('OPENAI_API_KEY'):
        doc_processor_ok = await test_document_processor()
    else:
        doc_processor_ok = True  # Skip if no OpenAI key
    
    # Test bot modules
    if imports_ok:
        bot_modules_ok = await test_bot_modules()
    else:
        bot_modules_ok = False
    
    # Print summary
    print_setup_summary(env_ok, missing_required, missing_optional)
    
    # Overall result
    print("\n" + "="*50)
    if imports_ok and env_ok and doc_processor_ok and bot_modules_ok:
        print("🎉 ALL TESTS PASSED! Bot is ready to run.")
        return 0
    else:
        print("❌ Some tests failed. Please fix the issues above.")
        return 1

if __name__ == "__main__":
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv(Path(__file__).parent / "backend" / ".env")
    
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

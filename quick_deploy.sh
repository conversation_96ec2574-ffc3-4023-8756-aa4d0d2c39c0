#!/bin/bash

# Quick Deployment Script for Lil' Gargs Discord Bot on Fly.io

set -e

echo "🚀 Quick Deploy - Lil' Gargs Discord Bot"
echo "========================================"

# Check if flyctl is installed
if ! command -v flyctl &> /dev/null; then
    echo "❌ flyctl not found. Install it first:"
    echo "   curl -L https://fly.io/install.sh | sh"
    exit 1
fi

# Check if logged in
if ! flyctl auth whoami &> /dev/null; then
    echo "❌ Not logged in to Fly.io. Run:"
    echo "   flyctl auth login"
    exit 1
fi

echo "✅ flyctl ready"

# Create app if it doesn't exist
APP_NAME="lil-gargs-discord-bot"
if ! flyctl apps list | grep -q "$APP_NAME"; then
    echo "📱 Creating app..."
    flyctl apps create "$APP_NAME"
else
    echo "✅ App exists"
fi

# Set secrets from .env file
if [ -f "backend/.env" ]; then
    echo "🔑 Setting secrets from .env..."
    
    # Extract values from .env
    DISCORD_TOKEN=$(grep "DISCORD_BOT_TOKEN" backend/.env | cut -d'=' -f2 | tr -d '"')
    GEMINI_KEY=$(grep "GEMINI_API_KEY" backend/.env | cut -d'=' -f2 | tr -d '"')
    HELIUS_KEY=$(grep "HELIUS_API_KEY" backend/.env | cut -d'=' -f2 | tr -d '"')
    MONGO_URL=$(grep "MONGO_URL" backend/.env | cut -d'=' -f2 | tr -d '"')
    
    # Set secrets
    if [ -n "$DISCORD_TOKEN" ]; then
        flyctl secrets set "DISCORD_BOT_TOKEN=$DISCORD_TOKEN"
        echo "✅ Discord token set"
    fi
    
    if [ -n "$GEMINI_KEY" ]; then
        flyctl secrets set "GEMINI_API_KEY=$GEMINI_KEY"
        echo "✅ Gemini API key set"
    fi
    
    if [ -n "$HELIUS_KEY" ]; then
        flyctl secrets set "HELIUS_API_KEY=$HELIUS_KEY"
        echo "✅ Helius API key set"
    fi
    
    if [ -n "$MONGO_URL" ]; then
        flyctl secrets set "MONGO_URL=$MONGO_URL"
        echo "✅ MongoDB URL set"
    fi
    
else
    echo "❌ No backend/.env file found!"
    echo "Create it with your API keys first."
    exit 1
fi

# Deploy
echo "🚀 Deploying..."
flyctl deploy

# Check status
echo "📊 Checking status..."
flyctl status

# Show logs
echo "📋 Recent logs:"
flyctl logs --lines 10

echo ""
echo "🎉 Deployment complete!"
echo ""
echo "Next steps:"
echo "1. Check health: https://$APP_NAME.fly.dev/health"
echo "2. Test in Discord: /systemhealth"
echo "3. Monitor logs: flyctl logs --follow"
echo ""
echo "Useful commands:"
echo "  flyctl logs           - View logs"
echo "  flyctl restart        - Restart bot"
echo "  flyctl status         - Check status"
echo "  flyctl secrets list   - List secrets"

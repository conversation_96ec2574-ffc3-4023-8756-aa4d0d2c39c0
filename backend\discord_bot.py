import discord
from discord.ext import commands, tasks
from discord import app_commands
import os
import asyncio
import logging
import random
from datetime import datetime, timedelta, timezone
from dotenv import load_dotenv
from motor.motor_asyncio import AsyncIOMotorClient
import uuid
from typing import Optional, Dict, List, Any
import json
import aiohttp
import base64
import base58

# Import Google Generative AI with error handling
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Google Generative AI not available: {e}")
    genai = None
    GEMINI_AVAILABLE = False

# Import document processing modules (with error handling)
try:
    from document_processor import DocumentProcessor
    from openai_integration import DocumentQASystem
    DOCUMENT_FEATURES_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Document processing features not available: {e}")
    DocumentProcessor = None
    DocumentQASystem = None
    DOCUMENT_FEATURES_AVAILABLE = False

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TicketView(discord.ui.View):
    """Persistent view for ticket creation button"""
    
    def __init__(self):
        super().__init__(timeout=None)
    
    @discord.ui.button(label='📩 Open Ticket', style=discord.ButtonStyle.primary, custom_id='create_ticket')
    async def create_ticket(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Create a new support ticket"""
        await interaction.response.defer(ephemeral=True)
        
        try:
            guild = interaction.guild
            user = interaction.user
            
            # Create clean username for channel name
            clean_username = ''.join(c.lower() for c in user.display_name if c.isalnum() or c in '-_')[:20]
            if not clean_username:
                clean_username = f"user{user.id}"
            
            # Check if user already has an open ticket
            existing_tickets = [ch for ch in guild.channels if ch.name == f'ticket-{clean_username}']
            if existing_tickets:
                await interaction.followup.send(
                    f"❌ You already have an open ticket! Please use your existing ticket channel: {existing_tickets[0].mention}",
                    ephemeral=True
                )
                return
            
            # Get bot instance
            bot = interaction.client
            
            # Create ticket channel
            overwrites = {
                guild.default_role: discord.PermissionOverwrite(read_messages=False),
                user: discord.PermissionOverwrite(read_messages=True, send_messages=True),
                guild.me: discord.PermissionOverwrite(read_messages=True, send_messages=True)
            }
            
            # Add support role if configured
            server_config = await bot.db.server_configs.find_one({"guild_id": str(guild.id)})
            if server_config and server_config.get('support_role_id'):
                try:
                    support_role = guild.get_role(int(server_config['support_role_id']))
                    if support_role:
                        overwrites[support_role] = discord.PermissionOverwrite(read_messages=True, send_messages=True)
                except:
                    pass
            
            # Create the ticket channel with username-based naming
            ticket_channel = await guild.create_text_channel(
                name=f'ticket-{clean_username}',
                overwrites=overwrites,
                topic=f'Support ticket for {user.display_name} (@{user.name})',
                reason=f'Ticket created by {user.display_name}'
            )
            
            # Create ticket embed
            embed = discord.Embed(
                title="🎫 Support Ticket Created",
                description=f"**User:** {user.mention} (@{user.name})\n**Created:** <t:{int(datetime.utcnow().timestamp())}:F>",
                color=0x00FF00
            )
            embed.add_field(
                name="📝 Instructions",
                value="Please describe your issue or question in detail. A support team member will assist you shortly.",
                inline=False
            )
            embed.set_footer(text="Use the button below to close this ticket when resolved.")
            
            # Create close ticket view
            close_view = CloseTicketView()
            
            await ticket_channel.send(f"👋 {user.mention} Welcome to your support ticket!", embed=embed, view=close_view)
            
            # Store ticket in database
            ticket_data = {
                "ticket_id": str(uuid.uuid4()),
                "user_id": str(user.id),
                "guild_id": str(guild.id),
                "channel_id": str(ticket_channel.id),
                "created_at": datetime.utcnow(),
                "status": "open",
                "messages": []
            }
            
            await bot.db.tickets.insert_one(ticket_data)
            
            # Log ticket creation
            if server_config and server_config.get('ticket_log_channel_id'):
                try:
                    log_channel = guild.get_channel(int(server_config['ticket_log_channel_id']))
                    if log_channel:
                        log_embed = discord.Embed(
                            title="🎫 New Ticket Created",
                            description=f"**User:** {user.mention} (@{user.name})\n**Channel:** {ticket_channel.mention}",
                            color=0x0099FF,
                            timestamp=datetime.utcnow()
                        )
                        await log_channel.send(embed=log_embed)
                except:
                    pass
            
            await interaction.followup.send(
                f"✅ **Ticket created successfully!**\n📋 Please check {ticket_channel.mention} to describe your issue.",
                ephemeral=True
            )
            
        except Exception as e:
            logger.error(f"Error creating ticket: {e}")
            await interaction.followup.send(
                "❌ Failed to create ticket. Please contact an administrator.",
                ephemeral=True
            )

class CloseTicketView(discord.ui.View):
    """View for closing tickets"""
    
    def __init__(self):
        super().__init__(timeout=None)
    
    @discord.ui.button(label='🔒 Close Ticket', style=discord.ButtonStyle.danger, custom_id='close_ticket')
    async def close_ticket(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Close the current ticket"""
        await interaction.response.defer()
        
        try:
            channel = interaction.channel
            guild = interaction.guild
            bot = interaction.client
            
            # Check if this is actually a ticket channel
            if not channel.name.startswith('ticket-'):
                await interaction.followup.send("❌ This command can only be used in ticket channels!")
                return
            
            # Update ticket in database
            await bot.db.tickets.update_one(
                {"channel_id": str(channel.id)},
                {
                    "$set": {
                        "status": "closed",
                        "closed_at": datetime.utcnow(),
                        "closed_by": str(interaction.user.id)
                    }
                }
            )
            
            # Create closing embed
            embed = discord.Embed(
                title="🔒 Ticket Closed",
                description=f"This ticket has been closed by {interaction.user.mention}",
                color=0xFF0000,
                timestamp=datetime.utcnow()
            )
            embed.add_field(
                name="📝 Note",
                value="This channel will be deleted in 10 seconds.",
                inline=False
            )
            
            await interaction.followup.send(embed=embed)
            
            # Log ticket closure
            server_config = await bot.db.server_configs.find_one({"guild_id": str(guild.id)})
            if server_config and server_config.get('ticket_log_channel_id'):
                try:
                    log_channel = guild.get_channel(int(server_config['ticket_log_channel_id']))
                    if log_channel:
                        log_embed = discord.Embed(
                            title="🔒 Ticket Closed",
                            description=f"**Channel:** {channel.name}\n**Closed by:** {interaction.user.mention}",
                            color=0xFF0000,
                            timestamp=datetime.utcnow()
                        )
                        await log_channel.send(embed=log_embed)
                except:
                    pass
            
            # Delete channel after delay
            await asyncio.sleep(10)
            await channel.delete(reason=f"Ticket closed by {interaction.user.display_name}")
            
        except Exception as e:
            logger.error(f"Error closing ticket: {e}")
            await interaction.followup.send("❌ Failed to close ticket!")

class LilGargsBot(commands.Bot):
    def __init__(self):
        # Configure bot intents
        intents = discord.Intents.default()
        intents.message_content = True
        intents.guilds = True
        intents.members = True
        
        super().__init__(
            command_prefix='!',
            intents=intents,
            help_command=None
        )
        
        # Database setup
        self.mongo_client = AsyncIOMotorClient(os.getenv('MONGO_URL'))
        self.db = self.mongo_client[os.getenv('DB_NAME')]
        
        # Bot configuration
        self.ai_enabled = True
        self.conversation_memory = {}  # Store conversation history per user
        self.ai_rate_limits = {}  # Track AI usage per user
        self.custom_documentation = {}  # Store custom documentation per guild
        self.start_time = datetime.now(timezone.utc)  # Track bot start time

        # Health check server
        self.health_server = None

        # API Keys
        self.gemini_api_key = os.getenv('GEMINI_API_KEY')
        self.helius_api_key = os.getenv('HELIUS_API_KEY')
        self.openai_api_key = os.getenv('OPENAI_API_KEY')

        # NFT Configuration
        self.nft_contract = os.getenv('NFT_CONTRACT_ADDRESS')
        self.verified_creator = os.getenv('VERIFIED_CREATOR')
        self.ipfs_image_folder = os.getenv('IPFS_IMAGE_FOLDER')
        self.ipfs_json_folder = os.getenv('IPFS_JSON_FOLDER')

        # Role configurations (will be set by admins)
        self.verified_role_id = None
        self.holder_role_id = None

        # Document processing systems (will be initialized in setup_hook)
        self.document_processor = None
        self.document_qa_system = None
        
    async def setup_hook(self):
        """Called when the bot is starting up"""
        logger.info("Setting up Lil' Gargs Bot...")
        
        # Add persistent views
        self.add_view(TicketView())
        self.add_view(CloseTicketView())
        
        # Create database collections and indexes
        await self.setup_database()
        
        # Load AI knowledge base
        await self.load_ai_knowledge_base()
        
        # Load server configurations
        await self.load_server_configs()

        # Initialize document processing systems
        await self.setup_document_systems()

        # Test Gemini API connection
        await self.test_gemini_connection()

        # Start health check server
        await self.start_health_server()

        # Sync commands
        try:
            synced = await self.tree.sync()
            logger.info(f"Synced {len(synced)} command(s)")
        except Exception as e:
            logger.error(f"Failed to sync commands: {e}")
    
    async def setup_database(self):
        """Initialize database collections"""
        collections = [
            'users', 'pets', 'battles', 'tickets', 'ai_knowledge',
            'server_configs', 'leaderboards', 'user_stats', 'nft_holders',
            'documents', 'document_chunks', 'qa_interactions', 'battle_challenges',
            'guild_documentation', 'error_logs'
        ]
        
        existing_collections = await self.db.list_collection_names()
        
        for collection in collections:
            if collection not in existing_collections:
                await self.db.create_collection(collection)
                logger.info(f"Created collection: {collection}")
                
        # Create indexes for better performance
        await self.db.nft_holders.create_index("user_id", unique=True)
        await self.db.nft_holders.create_index("wallet_address")
        await self.db.tickets.create_index("guild_id")
        await self.db.tickets.create_index("user_id")
        await self.db.tickets.create_index("status")
        await self.db.server_configs.create_index("guild_id", unique=True)

        # Document processing indexes
        await self.db.documents.create_index("guild_id")
        await self.db.documents.create_index("document_hash", unique=True)
        await self.db.document_chunks.create_index("document_id")
        await self.db.qa_interactions.create_index([("guild_id", 1), ("timestamp", -1)])
        await self.db.battle_challenges.create_index("battle_code")
    
    async def load_server_configs(self):
        """Load server-specific configurations"""
        # This will be populated by admin commands
        pass

    async def setup_document_systems(self):
        """Initialize document processing and QA systems"""
        try:
            if not DOCUMENT_FEATURES_AVAILABLE:
                logger.warning("Document processing modules not available - document features will be disabled")
                self.document_processor = None
                self.document_qa_system = None
                return

            if self.openai_api_key and DocumentProcessor and DocumentQASystem:
                # Initialize document processor
                db_name = os.getenv('DB_NAME', 'lil_gargs_bot')
                self.document_processor = DocumentProcessor(
                    openai_api_key=self.openai_api_key,
                    mongo_client=self.mongo_client,
                    db_name=db_name
                )

                # Initialize document QA system
                self.document_qa_system = DocumentQASystem(
                    openai_api_key=self.openai_api_key,
                    document_processor=self.document_processor
                )

                logger.info("Document processing systems initialized successfully")
            else:
                logger.warning("OpenAI API key not found or document modules missing - document features will be disabled")
                self.document_processor = None
                self.document_qa_system = None

        except Exception as e:
            logger.error(f"Failed to initialize document systems: {e}")
            self.document_processor = None
            self.document_qa_system = None

    async def test_gemini_connection(self):
        """Test Gemini API connection during startup"""
        try:
            if not self.gemini_api_key:
                logger.warning("Gemini API key not found - AI features will be disabled")
                self.ai_enabled = False
                return

            if not GEMINI_AVAILABLE or not genai:
                logger.warning("Google Generative AI library not available - AI features will be disabled")
                self.ai_enabled = False
                return

            # Configure and test Gemini API
            genai.configure(api_key=self.gemini_api_key)

            model = genai.GenerativeModel(
                model_name="gemini-1.5-flash",
                generation_config={
                    "temperature": 0.7,
                    "max_output_tokens": 50,
                }
            )

            # Test with a simple prompt
            test_response = model.generate_content("Say 'Gemini API connection successful' if you can read this.")

            if test_response.text:
                logger.info("✅ Gemini API connection successful")
                self.ai_enabled = True
            else:
                logger.error("❌ Gemini API test failed - no response received")
                self.ai_enabled = False

        except Exception as e:
            logger.error(f"❌ Gemini API connection failed: {e}")
            self.ai_enabled = False

            # Log specific error types
            if "API_KEY" in str(e).upper():
                logger.error("🔑 Invalid or missing Gemini API key")
            elif "QUOTA" in str(e).upper():
                logger.error("📊 Gemini API quota exceeded")
            else:
                logger.error(f"🔧 Gemini API error: {e}")

    async def start_health_server(self):
        """Start a simple health check server for Fly.io"""
        try:
            from aiohttp import web

            async def health_check(request):
                """Health check endpoint"""
                health_status = {
                    "status": "healthy",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "uptime_seconds": (datetime.now(timezone.utc) - self.start_time).total_seconds(),
                    "bot_ready": self.is_ready(),
                    "ai_enabled": self.ai_enabled,
                    "guilds": len(self.guilds) if self.is_ready() else 0
                }
                return web.json_response(health_status)

            app = web.Application()
            app.router.add_get('/health', health_check)
            app.router.add_get('/', health_check)  # Root endpoint

            runner = web.AppRunner(app)
            await runner.setup()
            site = web.TCPSite(runner, '0.0.0.0', 8080)
            await site.start()

            logger.info("Health check server started on port 8080")

        except Exception as e:
            logger.warning(f"Failed to start health check server: {e}")

    async def load_ai_knowledge_base(self):
        """Load initial AI knowledge base"""
        initial_knowledge = [
            {
                "id": str(uuid.uuid4()),
                "topic": "lil_gargs_project",
                "content": """Lil' Gargs is an NFT collection on Solana blockchain. 
                Key details:
                - Contract Address: FP2bGBGHWrW4w82hsSDGc5zNLQ83CvEmW2shGkttS7aZ
                - Verified Creator: 9fT6Spqbv9FxK7Ktxr6bDfASWc6k5acUNr1zMv5WrGfA
                - The collection features cute gargoyle-themed characters
                - Each NFT has unique traits and elements (Fire, Ice, Nature, Storm, Shadow)
                - The community is building around Discord and the pet/battle system""",
                "created_at": datetime.now(timezone.utc),
                "created_by": "system"
            }
        ]
        
        # Check if knowledge base is empty and populate it
        count = await self.db.ai_knowledge.count_documents({})
        if count == 0:
            await self.db.ai_knowledge.insert_many(initial_knowledge)
            logger.info("Loaded initial AI knowledge base")
    
    async def verify_nft_holder(self, wallet_address: str, user_id: str = None) -> Dict[str, Any]:
        """Enhanced NFT verification with security best practices and detailed feedback"""
        try:
            # Validate wallet address format
            if not self._is_valid_solana_address(wallet_address):
                return {
                    'success': False,
                    'error': 'Invalid Solana wallet address format. Please provide a valid base58 address.',
                    'holder': False,
                    'nft_count': 0,
                    'nfts': [],
                    'security_note': 'Only read-only verification performed - no private keys required.'
                }

            # Check if API key is available
            if not self.helius_api_key:
                return {
                    'success': False,
                    'error': 'NFT verification service is not configured. Please contact an administrator.',
                    'holder': False,
                    'nft_count': 0,
                    'nfts': [],
                    'security_note': 'Service temporarily unavailable.'
                }

            # Rate limiting check (if user_id provided)
            if user_id:
                rate_limit_result = await self._check_verification_rate_limit(user_id)
                if not rate_limit_result['allowed']:
                    return {
                        'success': False,
                        'error': f'Rate limit exceeded. Please wait {rate_limit_result["wait_time"]} seconds before trying again.',
                        'holder': False,
                        'nft_count': 0,
                        'nfts': [],
                        'security_note': 'Rate limiting protects against abuse.'
                    }

            # Use the correct Helius DAS API endpoint
            url = f"https://mainnet.helius-rpc.com/?api-key={self.helius_api_key}"

            # Prepare JSON-RPC request to get assets by owner
            payload = {
                "jsonrpc": "2.0",
                "id": f"lil-gargs-verification-{user_id or 'anonymous'}",
                "method": "getAssetsByOwner",
                "params": {
                    "ownerAddress": wallet_address,
                    "page": 1,
                    "limit": 1000,
                    "displayOptions": {
                        "showFungible": False,
                        "showNativeBalance": False
                    }
                }
            }

            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # Check if we got a valid response
                        if 'result' not in data:
                            logger.error(f"Invalid Helius API response: {data}")
                            return {
                                'success': False,
                                'error': 'Invalid API response from verification service.',
                                'holder': False,
                                'nft_count': 0,
                                'nfts': [],
                                'security_note': 'API response validation failed.'
                            }

                        assets = data['result'].get('items', [])

                        # Enhanced NFT verification with detailed collection info
                        lil_gargs_nfts = []
                        collection_stats = {'total_assets': len(assets), 'verified_collections': 0}

                        for asset in assets:
                            # Check if this is a Lil' Gargs NFT
                            creators = asset.get('creators', [])
                            for creator in creators:
                                if creator.get('address') == self.verified_creator and creator.get('verified', False):
                                    collection_stats['verified_collections'] += 1
                                    # This is a verified Lil' Gargs NFT
                                    content = asset.get('content', {})
                                    metadata = content.get('metadata', {})

                                    nft_info = {
                                        'id': asset.get('id'),
                                        'name': metadata.get('name', 'Unknown Lil Garg'),
                                        'image': content.get('files', [{}])[0].get('uri', ''),
                                        'attributes': metadata.get('attributes', []),
                                        'collection': asset.get('grouping', [{}])[0].get('group_value', 'Unknown'),
                                        'verified_creator': creator.get('address'),
                                        'verification_status': 'verified'
                                    }
                                    lil_gargs_nfts.append(nft_info)
                                    break

                        # Store verification result in database if user_id provided
                        if user_id and lil_gargs_nfts:
                            await self._store_verification_result(user_id, wallet_address, len(lil_gargs_nfts))

                        return {
                            'success': True,
                            'holder': len(lil_gargs_nfts) > 0,
                            'nft_count': len(lil_gargs_nfts),
                            'nfts': lil_gargs_nfts[:10],  # Limit to first 10 for display
                            'collection_stats': collection_stats,
                            'security_note': 'Verification completed using read-only blockchain data.',
                            'wallet_summary': f"Wallet contains {len(assets)} total NFTs, {len(lil_gargs_nfts)} are verified Lil' Gargs."
                        }
                    else:
                        logger.error(f"Helius API error: {response.status}")
                        response_text = await response.text()
                        logger.error(f"Response body: {response_text}")
                        return {
                            'success': False,
                            'error': f'Verification service error (Status: {response.status}). Please try again later.',
                            'holder': False,
                            'nft_count': 0,
                            'nfts': [],
                            'security_note': 'External API temporarily unavailable.'
                        }

        except asyncio.TimeoutError:
            logger.error("Timeout during NFT verification")
            return {
                'success': False,
                'error': 'Verification timed out. Please try again.',
                'holder': False,
                'nft_count': 0,
                'nfts': [],
                'security_note': 'Request timeout for security.'
            }
        except Exception as e:
            logger.error(f"Error verifying NFT holder: {e}")
            return {
                'success': False,
                'error': 'An unexpected error occurred during verification. Please try again later.',
                'holder': False,
                'nft_count': 0,
                'nfts': [],
                'security_note': 'Error logged for administrator review.'
            }

    async def _check_verification_rate_limit(self, user_id: str) -> Dict[str, Any]:
        """Check if user has exceeded verification rate limits"""
        try:
            current_time = datetime.utcnow()

            # Get user's recent verification attempts
            user_stats = await self.db.user_stats.find_one({"user_id": user_id})

            if not user_stats:
                # First time user, allow verification
                return {"allowed": True, "wait_time": 0}

            last_verification = user_stats.get('last_nft_verification')
            verification_count = user_stats.get('nft_verification_count_today', 0)
            last_reset = user_stats.get('nft_verification_reset_date')

            # Reset daily count if it's a new day
            if not last_reset or last_reset.date() != current_time.date():
                verification_count = 0

            # Check daily limit (10 verifications per day)
            if verification_count >= 10:
                return {
                    "allowed": False,
                    "wait_time": (24 * 3600) - (current_time.hour * 3600 + current_time.minute * 60 + current_time.second),
                    "reason": "Daily verification limit reached"
                }

            # Check cooldown (30 seconds between verifications)
            if last_verification:
                time_diff = (current_time - last_verification).total_seconds()
                if time_diff < 30:
                    return {
                        "allowed": False,
                        "wait_time": int(30 - time_diff),
                        "reason": "Cooldown period active"
                    }

            # Update verification tracking
            await self.db.user_stats.update_one(
                {"user_id": user_id},
                {
                    "$set": {
                        "last_nft_verification": current_time,
                        "nft_verification_reset_date": current_time
                    },
                    "$inc": {"nft_verification_count_today": 1}
                },
                upsert=True
            )

            return {"allowed": True, "wait_time": 0}

        except Exception as e:
            logger.error(f"Error checking verification rate limit: {e}")
            # On error, allow verification but log the issue
            return {"allowed": True, "wait_time": 0}

    async def _store_verification_result(self, user_id: str, wallet_address: str, nft_count: int):
        """Store successful verification result in database"""
        try:
            verification_data = {
                "user_id": user_id,
                "wallet_address": wallet_address,
                "nft_count": nft_count,
                "verified_at": datetime.utcnow(),
                "verification_type": "manual"
            }

            # Update or insert verification record
            await self.db.nft_holders.update_one(
                {"user_id": user_id},
                {"$set": verification_data},
                upsert=True
            )

            logger.info(f"Stored verification result for user {user_id}: {nft_count} NFTs")

        except Exception as e:
            logger.error(f"Error storing verification result: {e}")

    async def _check_ai_rate_limit(self, user_id: str) -> Dict[str, Any]:
        """Check if user has exceeded AI interaction rate limits"""
        try:
            current_time = datetime.utcnow()

            # Get user's AI usage stats
            user_stats = await self.db.user_stats.find_one({"user_id": user_id})

            if not user_stats:
                # First time user, allow AI interaction
                return {"allowed": True, "wait_time": 0}

            last_ai_request = user_stats.get('last_ai_request')
            ai_requests_today = user_stats.get('ai_requests_today', 0)
            ai_reset_date = user_stats.get('ai_reset_date')

            # Reset daily count if it's a new day
            if not ai_reset_date or ai_reset_date.date() != current_time.date():
                ai_requests_today = 0

            # Check daily limit (50 AI requests per day for regular users)
            daily_limit = 50
            if ai_requests_today >= daily_limit:
                return {
                    "allowed": False,
                    "wait_time": (24 * 3600) - (current_time.hour * 3600 + current_time.minute * 60 + current_time.second),
                    "reason": f"Daily AI request limit ({daily_limit}) reached"
                }

            # Check cooldown (3 seconds between AI requests)
            if last_ai_request:
                time_diff = (current_time - last_ai_request).total_seconds()
                if time_diff < 3:
                    return {
                        "allowed": False,
                        "wait_time": int(3 - time_diff),
                        "reason": "AI cooldown period active"
                    }

            # Update AI usage tracking
            await self.db.user_stats.update_one(
                {"user_id": user_id},
                {
                    "$set": {
                        "last_ai_request": current_time,
                        "ai_reset_date": current_time
                    },
                    "$inc": {"ai_requests_today": 1}
                },
                upsert=True
            )

            return {"allowed": True, "wait_time": 0}

        except Exception as e:
            logger.error(f"Error checking AI rate limit: {e}")
            # On error, allow AI interaction but log the issue
            return {"allowed": True, "wait_time": 0}

    async def _load_guild_documentation(self, guild_id: str) -> str:
        """Load custom documentation for a specific guild"""
        try:
            guild_docs = await self.db.guild_documentation.find_one({"guild_id": guild_id})

            if guild_docs and guild_docs.get('documentation'):
                return guild_docs['documentation']

            # Return default documentation if no custom docs found
            return """
            # Lil' Gargs Bot Documentation

            ## NFT Verification
            - Use `/nft verify <wallet_address>` to verify your Solana wallet contains Lil' Gargs NFTs
            - Verification is read-only and secure - no private keys required
            - Rate limited to prevent abuse (10 verifications per day, 30 second cooldown)

            ## AI Features
            - Mention the bot or use `/askgarg` to ask questions
            - Use `/suggest` for discussion prompts
            - Use `/gargoracle` for mystical advice
            - AI responses are rate limited (50 requests per day, 3 second cooldown)

            ## Gaming Features
            - Pet system with `/pet` commands
            - Battle system with `/battle` commands
            - Trivia games with `/game trivia`
            - Earn coins through various activities

            ## Community Features
            - Ticket system for support
            - Welcome messages for new members
            - Leaderboards and statistics
            """

        except Exception as e:
            logger.error(f"Error loading guild documentation: {e}")
            return "Documentation temporarily unavailable."

    async def _log_error(self, error_type: str, error_message: str, user_id: str = None, guild_id: str = None, additional_data: dict = None):
        """Log errors to database for monitoring and debugging"""
        try:
            error_log = {
                "error_type": error_type,
                "error_message": str(error_message),
                "user_id": user_id,
                "guild_id": guild_id,
                "timestamp": datetime.utcnow(),
                "additional_data": additional_data or {}
            }

            await self.db.error_logs.insert_one(error_log)

        except Exception as e:
            # Fallback to regular logging if database logging fails
            logger.error(f"Failed to log error to database: {e}")

    async def _check_system_health(self) -> Dict[str, Any]:
        """Check system health and return status"""
        try:
            health_status = {
                "timestamp": datetime.utcnow(),
                "database": "unknown",
                "ai_service": "unknown",
                "nft_service": "unknown",
                "errors_last_hour": 0
            }

            # Check database connectivity
            try:
                await self.db.admin.command('ping')
                health_status["database"] = "healthy"
            except Exception:
                health_status["database"] = "error"

            # Check AI service (Gemini)
            try:
                if not self.gemini_api_key:
                    health_status["ai_service"] = "not_configured"
                elif self.ai_enabled:
                    # Test actual API connection
                    genai.configure(api_key=self.gemini_api_key)
                    model = genai.GenerativeModel("gemini-1.5-flash")
                    test_response = model.generate_content("Test", generation_config={"max_output_tokens": 10})
                    if test_response.text:
                        health_status["ai_service"] = "healthy"
                    else:
                        health_status["ai_service"] = "error"
                else:
                    health_status["ai_service"] = "disabled"
            except Exception as e:
                health_status["ai_service"] = "error"
                logger.error(f"AI service health check failed: {e}")

            # Check NFT service (Helius)
            try:
                if self.helius_api_key:
                    health_status["nft_service"] = "configured"
                else:
                    health_status["nft_service"] = "not_configured"
            except Exception:
                health_status["nft_service"] = "error"

            # Count recent errors
            one_hour_ago = datetime.utcnow() - timedelta(hours=1)
            try:
                error_count = await self.db.error_logs.count_documents({
                    "timestamp": {"$gte": one_hour_ago}
                })
                health_status["errors_last_hour"] = error_count
            except Exception:
                health_status["errors_last_hour"] = "unknown"

            return health_status

        except Exception as e:
            logger.error(f"Error checking system health: {e}")
            return {"error": str(e), "timestamp": datetime.utcnow()}

    def _is_valid_solana_address(self, address: str) -> bool:
        """Validate Solana wallet address format"""
        try:
            # Solana addresses are base58 encoded and should be 32-44 characters
            if not address or len(address) < 32 or len(address) > 44:
                return False

            # Try to decode as base58
            decoded = base58.b58decode(address)

            # Solana public keys are 32 bytes
            return len(decoded) == 32

        except Exception:
            return False
    
    async def get_nft_metadata(self, token_id: str) -> Optional[Dict[str, Any]]:
        """Get NFT metadata from IPFS"""
        try:
            metadata_url = f"{self.ipfs_json_folder}/{token_id}.json"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(metadata_url) as response:
                    if response.status == 200:
                        metadata = await response.json()
                        
                        # Get image URL
                        image_url = f"{self.ipfs_image_folder}/{token_id}.png"
                        
                        return {
                            'name': metadata.get('name', f'Lil Garg #{token_id}'),
                            'description': metadata.get('description', ''),
                            'image': image_url,
                            'attributes': metadata.get('attributes', []),
                            'token_id': token_id
                        }
                    else:
                        return None
        except Exception as e:
            logger.error(f"Error fetching NFT metadata: {e}")
            return None
    
    async def get_ai_response(self, user_id: str, message: str, context: str = "general", guild_id: str = None) -> str:
        """Enhanced AI response with rate limiting, documentation integration, and improved context"""
        if not self.ai_enabled:
            return "AI responses are currently disabled."

        try:
            # Check rate limits
            rate_limit_result = await self._check_ai_rate_limit(user_id)
            if not rate_limit_result['allowed']:
                return f"⏱️ Rate limit exceeded. Please wait {rate_limit_result['wait_time']} seconds before asking again. ({rate_limit_result['reason']})"

            # Get user's conversation history
            session_id = f"user_{user_id}_{context}"

            # Load knowledge base for context
            knowledge_docs = await self.db.ai_knowledge.find().to_list(length=None)
            knowledge_context = "\n".join([doc['content'] for doc in knowledge_docs])

            # Load guild-specific documentation if available
            guild_documentation = ""
            if guild_id:
                guild_documentation = await self._load_guild_documentation(guild_id)

            # Enhanced system message with documentation and personality
            system_message = f"""You are an advanced AI assistant for the Lil' Gargs Discord community.
            You are knowledgeable, friendly, enthusiastic about NFTs and gaming, and deeply familiar with the project.

            ## Core Knowledge about Lil' Gargs:
            {knowledge_context}

            ## Additional Documentation:
            {guild_documentation}

            ## Response Guidelines:
            - Be helpful, engaging, and concise (under 500 words unless specifically asked for detail)
            - Use emojis appropriately to make responses more engaging
            - If asked about NFT verification, explain the security features (read-only, rate limited)
            - If asked about features not covered in documentation, be honest about limitations
            - Encourage community participation and engagement
            - For technical questions, provide accurate information based on the documentation
            - If someone asks about wallet verification, emphasize security best practices

            ## Context: {context}

            Respond naturally and be the helpful community assistant the Lil' Gargs deserve!"""

            # Configure Google Gemini API
            genai.configure(api_key=self.gemini_api_key)

            # Create the model
            model = genai.GenerativeModel(
                model_name="gemini-1.5-flash",
                generation_config={
                    "temperature": 0.7,
                    "top_p": 0.95,
                    "top_k": 64,
                    "max_output_tokens": 1000,
                }
            )

            # Get conversation history for context
            conversation_history = ""
            if user_id in self.conversation_memory:
                recent_exchanges = self.conversation_memory[user_id][-3:]  # Last 3 exchanges
                for exchange in recent_exchanges:
                    conversation_history += f"User: {exchange['user']}\nAI: {exchange['ai']}\n\n"

            # Combine system message with user message and history
            full_prompt = f"""{system_message}

## Recent Conversation History:
{conversation_history}

## Current User Message:
{message}

Please respond as the Lil' Gargs AI assistant:"""

            # Get response from Gemini
            response = model.generate_content(full_prompt)
            response_text = response.text if response.text else "I'm sorry, I couldn't generate a response. Please try again."
            
            # Store conversation in memory (simple cache)
            if user_id not in self.conversation_memory:
                self.conversation_memory[user_id] = []

            self.conversation_memory[user_id].append({
                "user": message,
                "ai": response_text,
                "timestamp": datetime.utcnow()
            })

            # Keep only last 10 exchanges to manage memory
            if len(self.conversation_memory[user_id]) > 10:
                self.conversation_memory[user_id] = self.conversation_memory[user_id][-10:]

            return response_text
            
        except genai.types.BlockedPromptException:
            logger.warning(f"Blocked prompt for user {user_id}: {message}")
            return "I can't respond to that type of message. Please try asking something else!"

        except genai.types.StopCandidateException:
            logger.warning(f"Response generation stopped for user {user_id}")
            return "I had to stop generating that response. Please try rephrasing your question!"

        except Exception as e:
            logger.error(f"AI response error: {e}")
            await self._log_error("ai_response", str(e), user_id, guild_id)

            # Check if it's an API key issue
            if "API_KEY" in str(e).upper() or "AUTHENTICATION" in str(e).upper():
                return "🔑 AI service is not properly configured. Please contact an administrator."
            elif "QUOTA" in str(e).upper() or "LIMIT" in str(e).upper():
                return "⚠️ AI service quota exceeded. Please try again later or contact an administrator."
            else:
                return "Sorry, I'm having trouble processing that right now. Please try again later! 🤖"
    
    async def on_ready(self):
        """Called when bot is ready"""
        logger.info(f'{self.user} has connected to Discord!')
        logger.info(f'Bot is in {len(self.guilds)} guilds')
        
        # Start background tasks
        if not hasattr(self, '_tasks_started'):
            self._tasks_started = True
            # Add any background tasks here if needed
    
    async def on_message(self, message):
        """Handle messages (for mentions and AI responses)"""
        if message.author == self.user:
            return
        
        # Check if bot was mentioned
        if self.user in message.mentions:
            # Remove the mention from the message
            content = message.content.replace(f'<@{self.user.id}>', '').strip()
            
            if content:
                # Get AI response with guild context
                response = await self.get_ai_response(
                    str(message.author.id),
                    content,
                    context="mention",
                    guild_id=str(message.guild.id) if message.guild else None
                )
                
                # Reply to the message
                await message.reply(response)
        
        # Process commands
        await self.process_commands(message)

# Initialize the bot
bot = LilGargsBot()

# ========================
# BATTLE SYSTEM VIEWS AND LOGIC
# ========================

class BattleView(discord.ui.View):
    """Interactive battle interface"""
    
    def __init__(self, battle_data):
        super().__init__(timeout=600)  # 10 minute timeout
        self.battle_data = battle_data
        
    async def on_timeout(self):
        """Handle battle timeout"""
        try:
            # Get the battle channel and delete it
            guild = discord.utils.get(bot.guilds, id=int(self.battle_data['guild_id']))
            if guild:
                battle_channel = guild.get_channel(int(self.battle_data['channel_id']))
                if battle_channel:
                    await battle_channel.send("⏰ **Battle timed out!** Channel will be deleted in 10 seconds...")
                    await asyncio.sleep(10)
                    await battle_channel.delete(reason="Battle timeout")
            
            # Update battle status in database
            await bot.db.battles.update_one(
                {"battle_id": self.battle_data['battle_id']},
                {"$set": {"status": "timeout", "ended_at": datetime.utcnow()}}
            )
        except Exception as e:
            logger.error(f"Battle timeout error: {e}")

    @discord.ui.button(label='⚔️ Attack', style=discord.ButtonStyle.danger, custom_id='battle_attack')
    async def battle_attack(self, interaction: discord.Interaction, button: discord.ui.Button):
        await self.handle_battle_action(interaction, "attack")
    
    @discord.ui.button(label='🛡️ Defend', style=discord.ButtonStyle.secondary, custom_id='battle_defend')
    async def battle_defend(self, interaction: discord.Interaction, button: discord.ui.Button):
        await self.handle_battle_action(interaction, "defend")
    
    @discord.ui.button(label='✨ Special', style=discord.ButtonStyle.primary, custom_id='battle_special')
    async def battle_special(self, interaction: discord.Interaction, button: discord.ui.Button):
        await self.handle_battle_action(interaction, "special")
    
    async def handle_battle_action(self, interaction: discord.Interaction, action: str):
        """Handle battle action selection"""
        await interaction.response.defer()
        
        try:
            user_id = str(interaction.user.id)
            battle = await bot.db.battles.find_one({"battle_id": self.battle_data['battle_id']})
            
            if not battle or battle['status'] != 'active':
                await interaction.followup.send("❌ This battle is no longer active!")
                return
            
            # Check if it's the user's turn
            current_turn = battle['current_turn']
            if current_turn != user_id:
                await interaction.followup.send("❌ It's not your turn!", ephemeral=True)
                return
            
            # Process the battle action
            result = await process_battle_turn(battle, user_id, action)
            
            # Update battle in database
            await bot.db.battles.update_one(
                {"battle_id": battle['battle_id']},
                {"$set": result['updated_battle']}
            )
            
            # Send battle result
            await interaction.followup.send(embed=result['embed'])
            
            # Check if battle ended
            if result['battle_ended']:
                # Award rewards and end battle
                await end_battle(battle, result['winner_id'])
                
                # Delete channel after delay
                await asyncio.sleep(30)
                try:
                    channel = interaction.channel
                    await channel.delete(reason="Battle completed")
                except:
                    pass
            else:
                # Continue battle with new view
                new_view = BattleView(self.battle_data)
                await interaction.edit_original_response(view=new_view)
                
        except Exception as e:
            logger.error(f"Battle action error: {e}")
            await interaction.followup.send("❌ Battle system error! Please try again.")

async def process_battle_turn(battle, user_id, action):
    """Process a single battle turn"""
    # Get pet data
    player1_pet = await bot.db.pets.find_one({"owner_id": battle['player1_id']})
    player2_pet = await bot.db.pets.find_one({"owner_id": battle['player2_id']})
    
    current_pet = player1_pet if user_id == battle['player1_id'] else player2_pet
    opponent_pet = player2_pet if user_id == battle['player1_id'] else player1_pet
    
    # Get current battle state
    battle_state = battle['battle_state']
    current_hp = battle_state[f"player{'1' if user_id == battle['player1_id'] else '2'}_hp"]
    opponent_hp = battle_state[f"player{'2' if user_id == battle['player1_id'] else '1'}_hp"]
    
    # Calculate damage and effects based on action and elements
    damage_dealt = 0
    damage_received = 0
    action_description = ""
    
    # Elemental advantages
    advantages = {
        "fire": {"strong_against": ["nature", "shadow"], "weak_to": ["ice", "storm"]},
        "ice": {"strong_against": ["storm", "shadow"], "weak_to": ["fire", "nature"]},
        "nature": {"strong_against": ["ice", "storm"], "weak_to": ["fire", "shadow"]},
        "storm": {"strong_against": ["fire", "shadow"], "weak_to": ["nature", "ice"]},
        "shadow": {"strong_against": ["fire", "ice"], "weak_to": ["nature", "storm"]}
    }
    
    # Base damage calculation
    base_attack = current_pet['attack']
    opponent_defense = opponent_pet['defense']
    
    if action == "attack":
        damage_dealt = max(1, base_attack - opponent_defense//2 + random.randint(-5, 10))
        
        # Apply elemental advantage
        if opponent_pet['element'] in advantages[current_pet['element']]['strong_against']:
            damage_dealt = int(damage_dealt * 1.3)
            action_description = f"🔥 **SUPER EFFECTIVE!** {current_pet['name']} deals {damage_dealt} damage!"
        elif opponent_pet['element'] in advantages[current_pet['element']]['weak_to']:
            damage_dealt = int(damage_dealt * 0.7)
            action_description = f"💧 Not very effective... {current_pet['name']} deals {damage_dealt} damage."
        else:
            action_description = f"⚔️ {current_pet['name']} attacks for {damage_dealt} damage!"
    
    elif action == "defend":
        damage_dealt = max(1, base_attack//3 + random.randint(1, 5))
        damage_received = max(0, damage_dealt - 10)  # Reduce incoming damage next turn
        action_description = f"🛡️ {current_pet['name']} takes a defensive stance and counterattacks for {damage_dealt} damage!"
    
    elif action == "special":
        # Special moves based on element
        special_moves = {
            "fire": {"name": "Flame Burst", "damage_mult": 1.5, "description": "🔥 unleashes a devastating flame burst"},
            "ice": {"name": "Frost Nova", "damage_mult": 1.4, "description": "❄️ creates a freezing blast"},
            "nature": {"name": "Vine Whip", "damage_mult": 1.3, "description": "🌿 summons powerful vines"},
            "storm": {"name": "Lightning Strike", "damage_mult": 1.6, "description": "⚡ calls down lightning"},
            "shadow": {"name": "Dark Pulse", "damage_mult": 1.4, "description": "🌑 channels dark energy"}
        }
        
        move = special_moves[current_pet['element']]
        damage_dealt = int((base_attack - opponent_defense//3) * move['damage_mult']) + random.randint(0, 8)
        action_description = f"✨ **{move['name']}!** {current_pet['name']} {move['description']} for {damage_dealt} damage!"
    
    # Apply damage
    new_opponent_hp = max(0, opponent_hp - damage_dealt)
    
    # Check if battle ended
    battle_ended = new_opponent_hp <= 0
    winner_id = user_id if battle_ended else None
    
    # Switch turns
    next_turn = battle['player2_id'] if user_id == battle['player1_id'] else battle['player1_id']
    
    # Update battle state
    updated_battle = {
        "current_turn": next_turn if not battle_ended else None,
        "turn_count": battle['turn_count'] + 1,
        "status": "completed" if battle_ended else "active",
        "battle_state": {
            **battle_state,
            f"player{'2' if user_id == battle['player1_id'] else '1'}_hp": new_opponent_hp
        }
    }
    
    if battle_ended:
        updated_battle["winner_id"] = winner_id
        updated_battle["ended_at"] = datetime.utcnow()
    
    # Create result embed
    embed = discord.Embed(
        title="⚔️ Battle Turn",
        description=action_description,
        color=0xFF0000 if battle_ended else 0x0099FF,
        timestamp=datetime.utcnow()
    )
    
    # Add HP bars
    player1_hp_percent = (battle_state["player1_hp"] / player1_pet['max_health']) * 100
    player2_hp_percent = (new_opponent_hp if user_id == battle['player1_id'] else battle_state["player2_hp"]) / (player2_pet if user_id == battle['player1_id'] else player1_pet)['max_health'] * 100
    
    if user_id == battle['player1_id']:
        player1_hp_current = battle_state["player1_hp"]
        player2_hp_current = new_opponent_hp
    else:
        player1_hp_current = new_opponent_hp
        player2_hp_current = battle_state["player2_hp"]
    
    embed.add_field(
        name=f"❤️ {player1_pet['name']} (Player 1)",
        value=f"**{player1_hp_current}/{player1_pet['max_health']} HP**\n{'🟢' * max(1, int(player1_hp_percent/10)) + '⚪' * (10 - max(1, int(player1_hp_percent/10)))}",
        inline=True
    )
    
    embed.add_field(
        name=f"❤️ {player2_pet['name']} (Player 2)",
        value=f"**{player2_hp_current}/{player2_pet['max_health']} HP**\n{'🟢' * max(1, int(player2_hp_percent/10)) + '⚪' * (10 - max(1, int(player2_hp_percent/10)))}",
        inline=True
    )
    
    if battle_ended:
        winner_pet = current_pet
        embed.add_field(
            name="🏆 Victory!",
            value=f"**{winner_pet['name']} wins the battle!**",
            inline=False
        )
    else:
        next_player = "Player 1" if next_turn == battle['player1_id'] else "Player 2"
        embed.add_field(
            name="⏳ Next Turn",
            value=f"**{next_player}'s turn**",
            inline=False
        )
    
    embed.set_footer(text=f"Turn {battle['turn_count'] + 1}")
    
    return {
        "updated_battle": updated_battle,
        "embed": embed,
        "battle_ended": battle_ended,
        "winner_id": winner_id
    }

async def end_battle(battle, winner_id):
    """Handle battle completion and rewards"""
    try:
        # Award rewards to winner
        winner_coins = 100
        winner_xp = 150
        loser_coins = 25
        loser_xp = 50
        
        # Update winner stats
        await bot.db.user_stats.update_one(
            {"user_id": winner_id},
            {
                "$inc": {
                    "coins": winner_coins,
                    "xp": winner_xp,
                    "battles_won": 1,
                    "battles_total": 1
                }
            },
            upsert=True
        )
        
        # Update loser stats
        loser_id = battle['player2_id'] if winner_id == battle['player1_id'] else battle['player1_id']
        await bot.db.user_stats.update_one(
            {"user_id": loser_id},
            {
                "$inc": {
                    "coins": loser_coins,
                    "xp": loser_xp,
                    "battles_total": 1
                }
            },
            upsert=True
        )
        
        # Update pet battle records
        await bot.db.pets.update_one(
            {"owner_id": winner_id},
            {
                "$inc": {
                    "battles_won": 1,
                    "total_battles": 1,
                    "xp": 20
                }
            }
        )
        
        await bot.db.pets.update_one(
            {"owner_id": loser_id},
            {
                "$inc": {
                    "total_battles": 1,
                    "xp": 10
                }
            }
        )
        
    except Exception as e:
        logger.error(f"End battle error: {e}")

# ========================
# SLASH COMMANDS - BATTLE SYSTEM
# ========================

@bot.tree.command(name="battle", description="Pet battle system")
@app_commands.describe(
    action="Choose a battle action",
    opponent="User to challenge (for start action)",
    code="Battle code to accept (for accept action)"
)
@app_commands.choices(action=[
    app_commands.Choice(name="start", value="start"),
    app_commands.Choice(name="accept", value="accept"),
    app_commands.Choice(name="arena", value="arena"),
    app_commands.Choice(name="profile", value="profile")
])
async def battle_command(interaction: discord.Interaction, action: str, opponent: discord.Member = None, code: str = None):
    """Turn-based pet battle system"""
    await interaction.response.defer()
    
    user_id = str(interaction.user.id)
    
    if action == "start":
        if not opponent:
            await interaction.followup.send("❌ You must specify an opponent to challenge!")
            return
        
        if opponent.id == interaction.user.id:
            await interaction.followup.send("❌ You can't battle yourself!")
            return
        
        if opponent.bot:
            await interaction.followup.send("❌ You can't battle bots!")
            return
        
        try:
            # Check if both users have pets
            user_pet = await bot.db.pets.find_one({"owner_id": user_id})
            opponent_pet = await bot.db.pets.find_one({"owner_id": str(opponent.id)})
            
            if not user_pet:
                await interaction.followup.send("❌ You need a pet to battle! Use `/pet adopt` first.")
                return
            
            if not opponent_pet:
                await interaction.followup.send(f"❌ {opponent.display_name} doesn't have a pet to battle!")
                return
            
            # Check if pets have enough health and energy
            if user_pet['health'] < user_pet['max_health'] * 0.3:
                await interaction.followup.send("❌ Your pet is too injured to battle! Use `/pet feed` to restore health.")
                return
            
            if opponent_pet['health'] < opponent_pet['max_health'] * 0.3:
                await interaction.followup.send(f"❌ {opponent.display_name}'s pet is too injured to battle!")
                return
            
            # Create battle challenge
            battle_code = ''.join(random.choices('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=6))
            
            challenge = {
                "challenge_id": str(uuid.uuid4()),
                "challenger_id": user_id,
                "opponent_id": str(opponent.id),
                "battle_code": battle_code,
                "guild_id": str(interaction.guild.id),
                "created_at": datetime.utcnow(),
                "status": "pending"
            }
            
            await bot.db.battle_challenges.insert_one(challenge)
            
            embed = discord.Embed(
                title="⚔️ Battle Challenge!",
                description=f"**{interaction.user.display_name}** has challenged **{opponent.display_name}** to a pet battle!",
                color=0xFF6B6B,
                timestamp=datetime.utcnow()
            )
            
            embed.add_field(
                name="🐾 Challenger",
                value=f"**{user_pet['name']}** (Level {user_pet['level']})\n{get_element_emoji(user_pet['element'])} {user_pet['element'].title()}",
                inline=True
            )
            
            embed.add_field(
                name="🎯 Challenged",
                value=f"**{opponent_pet['name']}** (Level {opponent_pet['level']})\n{get_element_emoji(opponent_pet['element'])} {opponent_pet['element'].title()}",
                inline=True
            )
            
            embed.add_field(
                name="📋 Battle Code",
                value=f"**{battle_code}**",
                inline=False
            )
            
            embed.add_field(
                name="⏰ How to Accept",
                value=f"{opponent.mention} use `/battle accept code:{battle_code}` to accept this challenge!",
                inline=False
            )
            
            embed.set_footer(text="Challenge expires in 5 minutes")
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Battle start error: {e}")
            await interaction.followup.send("❌ Failed to create battle challenge!")
    
    elif action == "accept":
        if not code:
            await interaction.followup.send("❌ You must provide a battle code to accept!")
            return
        
        try:
            # Find the challenge
            challenge = await bot.db.battle_challenges.find_one({
                "battle_code": code.upper(),
                "opponent_id": user_id,
                "status": "pending"
            })
            
            if not challenge:
                await interaction.followup.send("❌ Invalid battle code or challenge not found!")
                return
            
            # Check if challenge expired (5 minutes)
            if datetime.utcnow() - challenge['created_at'] > timedelta(minutes=5):
                await interaction.followup.send("❌ This battle challenge has expired!")
                await bot.db.battle_challenges.update_one(
                    {"challenge_id": challenge['challenge_id']},
                    {"$set": {"status": "expired"}}
                )
                return
            
            # Create battle channel
            guild = interaction.guild
            
            # Set permissions for battle channel
            overwrites = {
                guild.default_role: discord.PermissionOverwrite(read_messages=False),
                interaction.user: discord.PermissionOverwrite(read_messages=True, send_messages=True),
                guild.me: discord.PermissionOverwrite(read_messages=True, send_messages=True)
            }
            
            # Add challenger to permissions
            challenger = guild.get_member(int(challenge['challenger_id']))
            if challenger:
                overwrites[challenger] = discord.PermissionOverwrite(read_messages=True, send_messages=True)
            
            battle_channel = await guild.create_text_channel(
                name=f'battle-{challenge["challenger_id"][:4]}-vs-{user_id[:4]}',
                overwrites=overwrites,
                topic=f'Pet battle between {challenger.display_name if challenger else "Unknown"} and {interaction.user.display_name}',
                reason='Pet battle arena'
            )
            
            # Get pets
            challenger_pet = await bot.db.pets.find_one({"owner_id": challenge['challenger_id']})
            opponent_pet = await bot.db.pets.find_one({"owner_id": user_id})
            
            # Create battle data
            battle_data = {
                "battle_id": str(uuid.uuid4()),
                "player1_id": challenge['challenger_id'],
                "player2_id": user_id,
                "guild_id": str(guild.id),
                "channel_id": str(battle_channel.id),
                "status": "active",
                "current_turn": challenge['challenger_id'],  # Challenger goes first
                "turn_count": 0,
                "battle_state": {
                    "player1_hp": challenger_pet['health'],
                    "player2_hp": opponent_pet['health']
                },
                "started_at": datetime.utcnow()
            }
            
            await bot.db.battles.insert_one(battle_data)
            
            # Update challenge status
            await bot.db.battle_challenges.update_one(
                {"challenge_id": challenge['challenge_id']},
                {"$set": {"status": "accepted", "battle_id": battle_data['battle_id']}}
            )
            
            # Create battle start embed
            embed = discord.Embed(
                title="⚔️ BATTLE BEGINS!",
                description=f"**{challenger_pet['name']}** vs **{opponent_pet['name']}**",
                color=0x00FF00,
                timestamp=datetime.utcnow()
            )
            
            embed.add_field(
                name=f"🔥 {challenger_pet['name']} (Player 1)",
                value=f"**Level:** {challenger_pet['level']}\n**Element:** {get_element_emoji(challenger_pet['element'])} {challenger_pet['element'].title()}\n**HP:** {challenger_pet['health']}/{challenger_pet['max_health']}",
                inline=True
            )
            
            embed.add_field(
                name=f"❄️ {opponent_pet['name']} (Player 2)",
                value=f"**Level:** {opponent_pet['level']}\n**Element:** {get_element_emoji(opponent_pet['element'])} {opponent_pet['element'].title()}\n**HP:** {opponent_pet['health']}/{opponent_pet['max_health']}",
                inline=True
            )
            
            embed.add_field(
                name="⏳ Current Turn",
                value=f"**Player 1** ({challenger.display_name if challenger else 'Unknown'})",
                inline=False
            )
            
            embed.set_footer(text="Choose your action! Battle times out after 10 minutes of inactivity.")
            
            # Create battle view
            view = BattleView(battle_data)
            
            await battle_channel.send(f"{challenger.mention if challenger else ''} {interaction.user.mention}", embed=embed, view=view)
            
            await interaction.followup.send(f"⚔️ Battle accepted! Head to {battle_channel.mention} to fight!")
            
        except Exception as e:
            logger.error(f"Battle accept error: {e}")
            await interaction.followup.send("❌ Failed to accept battle challenge!")
    
    elif action == "arena":
        try:
            # Show active battles
            active_battles = await bot.db.battles.find({
                "guild_id": str(interaction.guild.id),
                "status": "active"
            }).to_list(length=10)
            
            if not active_battles:
                await interaction.followup.send("🏟️ No active battles in the arena right now!")
                return
            
            embed = discord.Embed(
                title="🏟️ Battle Arena",
                description="*Active battles in progress*",
                color=0x9932CC,
                timestamp=datetime.utcnow()
            )
            
            for i, battle in enumerate(active_battles):
                try:
                    player1 = bot.get_user(int(battle['player1_id']))
                    player2 = bot.get_user(int(battle['player2_id']))
                    
                    player1_name = player1.display_name if player1 else "Unknown"
                    player2_name = player2.display_name if player2 else "Unknown"
                    
                    channel = interaction.guild.get_channel(int(battle['channel_id']))
                    channel_mention = channel.mention if channel else "Channel deleted"
                    
                    battle_duration = datetime.utcnow() - battle['started_at']
                    duration_mins = int(battle_duration.total_seconds() // 60)
                    
                    embed.add_field(
                        name=f"⚔️ Battle {i+1}",
                        value=f"**{player1_name}** vs **{player2_name}**\n**Duration:** {duration_mins}m\n**Channel:** {channel_mention}",
                        inline=True
                    )
                except:
                    continue
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Battle arena error: {e}")
            await interaction.followup.send("❌ Failed to load battle arena!")
    
    elif action == "profile":
        try:
            # Get user's battle stats
            user_stats = await bot.db.user_stats.find_one({"user_id": user_id})
            pet = await bot.db.pets.find_one({"owner_id": user_id})
            
            if not user_stats:
                user_stats = {"battles_won": 0, "battles_total": 0}
            
            battles_won = user_stats.get('battles_won', 0)
            battles_total = user_stats.get('battles_total', 0)
            win_rate = f"{battles_won}/{battles_total}" if battles_total > 0 else "0/0"
            win_percentage = int((battles_won / battles_total) * 100) if battles_total > 0 else 0
            
            embed = discord.Embed(
                title=f"⚔️ {interaction.user.display_name}'s Battle Profile",
                color=interaction.user.color or 0xFF6B6B,
                timestamp=datetime.utcnow()
            )
            embed.set_thumbnail(url=interaction.user.display_avatar.url)
            
            embed.add_field(
                name="📊 Battle Stats",
                value=f"**Wins:** {battles_won}\n**Total:** {battles_total}\n**Win Rate:** {win_percentage}%",
                inline=True
            )
            
            if pet:
                pet_battles_won = pet.get('battles_won', 0)
                pet_battles_total = pet.get('total_battles', 0)
                pet_win_rate = f"{pet_battles_won}/{pet_battles_total}" if pet_battles_total > 0 else "0/0"
                
                embed.add_field(
                    name=f"🐾 {pet['name']}'s Record",
                    value=f"**Level:** {pet['level']}\n**Battles:** {pet_win_rate}\n**Element:** {get_element_emoji(pet['element'])} {pet['element'].title()}",
                    inline=True
                )
            
            # Calculate rank
            if battles_total > 0:
                rank_query = await bot.db.user_stats.count_documents({"battles_won": {"$gt": battles_won}})
                rank = rank_query + 1
                embed.add_field(
                    name="🏆 Arena Rank",
                    value=f"**#{rank}**",
                    inline=True
                )
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Battle profile error: {e}")
            await interaction.followup.send("❌ Failed to load battle profile!")

def get_element_emoji(element):
    """Get emoji for element"""
    emojis = {
        "fire": "🔥", "ice": "❄️", "nature": "🌿", "storm": "⚡", "shadow": "🌑"
    }
    return emojis.get(element, "❓")

# ========================
# SLASH COMMANDS - ENHANCED GAME FEATURES
# ========================

@bot.tree.command(name="game", description="Lil' Gargs community games")
@app_commands.describe(
    action="Choose a game action",
    difficulty="Trivia difficulty (easy/medium/hard)",
    element="Your element choice for elemental fight",
    bet="Coins to bet on dice roll"
)
@app_commands.choices(
    action=[
        app_commands.Choice(name="trivia", value="trivia"),
        app_commands.Choice(name="elemental-fight", value="elemental_fight"),
        app_commands.Choice(name="dailyquest", value="dailyquest"),
        app_commands.Choice(name="profile", value="profile"),
        app_commands.Choice(name="leaderboard", value="leaderboard"),
        app_commands.Choice(name="guessgarg", value="guessgarg"),
        app_commands.Choice(name="dice", value="dice")
    ],
    difficulty=[
        app_commands.Choice(name="easy", value="easy"),
        app_commands.Choice(name="medium", value="medium"),
        app_commands.Choice(name="hard", value="hard")
    ],
    element=[
        app_commands.Choice(name="🔥 Fire", value="fire"),
        app_commands.Choice(name="❄️ Ice", value="ice"),
        app_commands.Choice(name="🌿 Nature", value="nature"),
        app_commands.Choice(name="⚡ Storm", value="storm"),
        app_commands.Choice(name="🌑 Shadow", value="shadow")
    ]
)
async def game_command(interaction: discord.Interaction, action: str, difficulty: str = "medium", element: str = None, bet: int = None):
    """Enhanced Lil' Gargs themed games"""
    await interaction.response.defer()
    
    user_id = str(interaction.user.id)
    
    if action == "trivia":
        try:
            # Check if user is already in a trivia session
            user_stats = await bot.db.user_stats.find_one({"user_id": user_id})
            if user_stats and user_stats.get('trivia_session', {}).get('active'):
                await interaction.followup.send("❌ You already have an active trivia session! Type **stop** to end it or use `/triviastop`")
                return
            
            # Get trivia questions
            trivia_questions = get_trivia_question_pool()
            
            rewards = {
                "easy": {"coins": 15, "xp": 30},
                "medium": {"coins": 25, "xp": 50},
                "hard": {"coins": 40, "xp": 80}
            }
            
            # Get user's trivia history to avoid recent repeats
            recent_questions = user_stats.get('recent_trivia_questions', []) if user_stats else []
            
            # Filter out recently asked questions
            available_questions = []
            for q in trivia_questions[difficulty]:
                if q['question'] not in recent_questions:
                    available_questions.append(q)
            
            # If all questions were recent, reset the history
            if not available_questions:
                available_questions = trivia_questions[difficulty]
                recent_questions = []
            
            # Select random question
            selected_question = random.choice(available_questions)
            
            # Update recent questions history (keep last 5)
            recent_questions.append(selected_question['question'])
            if len(recent_questions) > 5:
                recent_questions = recent_questions[-5:]
            
            # Start continuous trivia session
            await bot.db.user_stats.update_one(
                {"user_id": user_id},
                {
                    "$set": {
                        "trivia_session": {
                            "active": True,
                            "difficulty": difficulty,
                            "started_at": datetime.utcnow(),
                            "questions_answered": 0,
                            "correct_answers": 0
                        },
                        "current_trivia": {
                            "question": selected_question['question'],
                            "correct_answer": selected_question['answer'],
                            "difficulty": difficulty,
                            "rewards": rewards[difficulty],
                            "timestamp": datetime.utcnow()
                        },
                        "recent_trivia_questions": recent_questions
                    }
                },
                upsert=True
            )
            
            embed = discord.Embed(
                title=f"🧠 Continuous Trivia Started! ({difficulty.title()})",
                description=f"**Question 1:** {selected_question['question']}",
                color=0x7289DA,
                timestamp=datetime.utcnow()
            )
            
            option_text = '\n'.join(selected_question['options'])
            embed.add_field(name="🎯 Options", value=option_text, inline=False)
            embed.add_field(
                name="🎮 How to Play", 
                value="**Reply with A, B, C, or D**\nType **stop** to end session\nEarn bonus coins for high accuracy!\nQuestions continue automatically!", 
                inline=False
            )
            embed.add_field(
                name="🎁 Rewards Per Question", 
                value=f"**{rewards[difficulty]['coins']} coins** • **{rewards[difficulty]['xp']} XP**", 
                inline=True
            )
            embed.set_footer(text=f"Continuous mode active • {difficulty.title()} difficulty")
            
            await interaction.followup.send(embed=embed)
        
        except Exception as e:
            logger.error(f"Trivia error: {e}")
            await interaction.followup.send("❌ The ancient trivia spirits are resting. Try again later!")
    
    elif action == "elemental_fight":
        if not element:
            await interaction.followup.send("❌ You must choose an element! Use `/game elemental-fight element:[choice]`")
            return
        
        try:
            elements = ["fire", "ice", "nature", "storm", "shadow"]
            enemy_element = random.choice(elements)
            
            # Element advantages
            advantages = {
                "fire": {"weak_to": ["ice", "storm"], "strong_against": ["nature", "shadow"]},
                "ice": {"weak_to": ["fire", "nature"], "strong_against": ["storm", "shadow"]},
                "nature": {"weak_to": ["fire", "shadow"], "strong_against": ["ice", "storm"]},
                "storm": {"weak_to": ["nature", "ice"], "strong_against": ["fire", "shadow"]},
                "shadow": {"weak_to": ["nature", "storm"], "strong_against": ["fire", "ice"]}
            }
            
            element_emojis = {
                "fire": "🔥", "ice": "❄️", "nature": "🌿", "storm": "⚡", "shadow": "🌑"
            }
            
            user_power = random.randint(50, 100)
            enemy_power = random.randint(50, 100)
            
            # Apply elemental advantages
            if enemy_element in advantages[element]["strong_against"]:
                user_power += 25
                advantage = f"✅ Your {element_emojis[element]} {element.title()} has advantage over {element_emojis[enemy_element]} {enemy_element.title()}!"
            elif enemy_element in advantages[element]["weak_to"]:
                user_power -= 20
                advantage = f"❌ Your {element_emojis[element]} {element.title()} is weak against {element_emojis[enemy_element]} {enemy_element.title()}!"
            else:
                advantage = f"⚖️ Neutral matchup: {element_emojis[element]} {element.title()} vs {element_emojis[enemy_element]} {enemy_element.title()}"
            
            # Determine winner
            if user_power > enemy_power:
                result = "victory"
                coins_earned = 30
                xp_earned = 60
                result_text = f"🎉 **VICTORY!** You defeated the {enemy_element.title()} guardian!"
                color = 0x00FF00
            elif user_power < enemy_power:
                result = "defeat"
                coins_earned = 10
                xp_earned = 20
                result_text = f"💀 **DEFEAT!** The {enemy_element.title()} guardian was too strong..."
                color = 0xFF0000
            else:
                result = "draw"
                coins_earned = 20
                xp_earned = 40
                result_text = f"🤝 **DRAW!** An epic stalemate with the {enemy_element.title()} guardian!"
                color = 0xFFFF00
            
            # Award rewards
            await bot.db.user_stats.update_one(
                {"user_id": user_id},
                {
                    "$inc": {
                        "coins": coins_earned,
                        "xp": xp_earned,
                        f"elemental_fights_{result}": 1,
                        "elemental_fights_total": 1
                    }
                },
                upsert=True
            )
            
            embed = discord.Embed(
                title="⚔️ Elemental Challenge",
                description=result_text,
                color=color,
                timestamp=datetime.utcnow()
            )
            
            embed.add_field(
                name="🔮 Battle Details",
                value=f"**Your Power:** {user_power}\n**Enemy Power:** {enemy_power}",
                inline=True
            )
            
            embed.add_field(
                name="🎁 Rewards",
                value=f"**{coins_earned} coins** • **{xp_earned} XP**",
                inline=True
            )
            
            embed.add_field(
                name="⚡ Elemental Effect",
                value=advantage,
                inline=False
            )
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Elemental fight error: {e}")
            await interaction.followup.send("❌ The elemental guardians are not responding. Try again later!")
    
    elif action == "guessgarg":
        try:
            # Random NFT guessing game
            traits = [
                "Fiery Horns", "Ice Crystal Wings", "Nature Vine Crown", "Storm Lightning Eyes", "Shadow Cloak",
                "Golden Scales", "Silver Fangs", "Ruby Gem", "Emerald Eyes", "Sapphire Wings",
                "Ancient Runes", "Mystic Aura", "Crystal Armor", "Flame Breath", "Frost Touch"
            ]
            
            elements = ["Fire", "Ice", "Nature", "Storm", "Shadow"]
            personalities = ["Bold", "Calm", "Clever", "Wild"]
            
            # Generate random Garg
            correct_trait = random.choice(traits)
            correct_element = random.choice(elements)
            correct_personality = random.choice(personalities)
            
            # Create multiple choice
            trait_options = [correct_trait]
            while len(trait_options) < 4:
                random_trait = random.choice(traits)
                if random_trait not in trait_options:
                    trait_options.append(random_trait)
            
            random.shuffle(trait_options)
            correct_index = trait_options.index(correct_trait) + 1
            
            embed = discord.Embed(
                title="🔍 Guess the Garg!",
                description=f"A mysterious **{correct_element}** Garg with a **{correct_personality}** personality appears!\n\nWhat special trait does it have?",
                color=0x9932CC,
                timestamp=datetime.utcnow()
            )
            
            options_text = ""
            for i, trait in enumerate(trait_options):
                options_text += f"**{i+1}.** {trait}\n"
            
            embed.add_field(name="🎯 Options", value=options_text, inline=False)
            embed.add_field(name="🎁 Reward", value="**25 coins** • **50 XP**", inline=True)
            embed.add_field(name="⏰ Time", value="30 seconds", inline=True)
            
            # Store guess game
            await bot.db.user_stats.update_one(
                {"user_id": user_id},
                {
                    "$set": {
                        "current_guess_game": {
                            "correct_answer": str(correct_index),
                            "trait": correct_trait,
                            "timestamp": datetime.utcnow()
                        }
                    }
                },
                upsert=True
            )
            
            embed.set_footer(text=f"Reply with the number (1-4) of your guess!")
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Guess garg error: {e}")
            await interaction.followup.send("❌ The mystical Gargs are hiding. Try again later!")
    
    elif action == "dice":
        if bet is None:
            await interaction.followup.send("❌ You must bet some coins! Use `/game dice bet:[amount]`")
            return
        
        if bet < 5 or bet > 100:
            await interaction.followup.send("❌ Bet must be between 5 and 100 coins!")
            return
        
        try:
            # Check if user has enough coins
            user_stats = await bot.db.user_stats.find_one({"user_id": user_id})
            current_coins = user_stats.get('coins', 0) if user_stats else 0
            
            if current_coins < bet:
                await interaction.followup.send(f"❌ You only have {current_coins} coins! You need {bet} to play.")
                return
            
            # Roll dice
            user_roll = random.randint(1, 6)
            bot_roll = random.randint(1, 6)
            
            # Determine outcome
            if user_roll > bot_roll:
                coins_change = bet
                result = "won"
                color = 0x00FF00
                result_text = f"🎉 **YOU WIN!** Your {user_roll} beats {bot_roll}!"
            elif user_roll < bot_roll:
                coins_change = -bet
                result = "lost"
                color = 0xFF0000
                result_text = f"💸 **YOU LOSE!** Your {user_roll} loses to {bot_roll}!"
            else:
                coins_change = 0
                result = "tied"
                color = 0xFFFF00
                result_text = f"🤝 **TIE!** Both rolled {user_roll}! Bet returned."
            
            # Update coins
            await bot.db.user_stats.update_one(
                {"user_id": user_id},
                {
                    "$inc": {
                        "coins": coins_change,
                        f"dice_games_{result}": 1,
                        "dice_games_total": 1
                    }
                },
                upsert=True
            )
            
            embed = discord.Embed(
                title="🎲 Garg Dice Roll",
                description=result_text,
                color=color,
                timestamp=datetime.utcnow()
            )
            
            embed.add_field(
                name="🎯 Rolls",
                value=f"**You:** 🎲 {user_roll}\n**Bot:** 🎲 {bot_roll}",
                inline=True
            )
            
            new_balance = current_coins + coins_change
            embed.add_field(
                name="💰 Coins",
                value=f"**Change:** {coins_change:+d}\n**Balance:** {new_balance}",
                inline=True
            )
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Dice error: {e}")
            await interaction.followup.send("❌ The dice spirits are confused. Try again!")
    
    elif action == "dailyquest":
        try:
            # Check if user already completed daily quest
            today = datetime.utcnow().date()
            user_stats = await bot.db.user_stats.find_one({"user_id": user_id})
            
            last_quest_date = None
            if user_stats and 'last_daily_quest' in user_stats:
                last_quest_date = user_stats['last_daily_quest'].date()
            
            if last_quest_date == today:
                embed = discord.Embed(
                    title="✅ Daily Quest Complete",
                    description="You've already completed today's quest! Come back tomorrow for a new challenge.",
                    color=0x0099FF
                )
                await interaction.followup.send(embed=embed)
                return
            
            # Themed daily quests
            daily_quests = [
                {
                    "title": "🔥 Elemental Mastery",
                    "description": "Channel the power of the elements! Win an elemental fight to prove your strength.",
                    "reward_coins": 75,
                    "reward_xp": 150
                },
                {
                    "title": "🧠 Scholar of Gargs",
                    "description": "Test your knowledge! Answer a trivia question correctly to gain wisdom.",
                    "reward_coins": 60,
                    "reward_xp": 120
                },
                {
                    "title": "🎲 Fortune's Favor",
                    "description": "Take a risk! Play the dice game and let fate decide your path.",
                    "reward_coins": 50,
                    "reward_xp": 100
                },
                {
                    "title": "🔍 Mystery Hunter",
                    "description": "Solve the riddle of the realm! Successfully guess a Garg's trait.",
                    "reward_coins": 65,
                    "reward_xp": 130
                },
                {
                    "title": "🤝 Community Spirit",
                    "description": "Spread positivity in the server! Help another member or share encouraging words.",
                    "reward_coins": 80,
                    "reward_xp": 160
                }
            ]
            
            quest = random.choice(daily_quests)
            
            # Award quest completion immediately
            await bot.db.user_stats.update_one(
                {"user_id": user_id},
                {
                    "$inc": {
                        "coins": quest["reward_coins"],
                        "xp": quest["reward_xp"],
                        "daily_quests_completed": 1
                    },
                    "$set": {"last_daily_quest": datetime.utcnow()}
                },
                upsert=True
            )
            
            embed = discord.Embed(
                title=quest["title"],
                description=quest["description"],
                color=0xFFD700,
                timestamp=datetime.utcnow()
            )
            embed.add_field(
                name="🎁 Rewards Earned",
                value=f"**{quest['reward_coins']} coins** • **{quest['reward_xp']} XP**",
                inline=True
            )
            embed.set_footer(text="Quest completed! Return tomorrow for a new challenge.")
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Daily quest error: {e}")
            await interaction.followup.send("❌ The quest spirits are unavailable. Try again later!")
    
    elif action == "profile":
        try:
            # Get comprehensive user stats
            user_stats = await bot.db.user_stats.find_one({"user_id": user_id})
            
            if not user_stats:
                # Initialize user stats
                user_stats = {
                    "user_id": user_id,
                    "xp": 0,
                    "coins": 0,
                    "trivia_correct": 0,
                    "trivia_total": 0,
                    "daily_quests_completed": 0,
                    "elemental_fights_victory": 0,
                    "elemental_fights_total": 0
                }
                await bot.db.user_stats.insert_one(user_stats)
            
            # Calculate level and titles
            xp = user_stats.get('xp', 0)
            level = xp // 100
            xp_for_next_level = 100 - (xp % 100)
            
            # Determine title based on achievements
            titles = []
            if user_stats.get('daily_quests_completed', 0) >= 30:
                titles.append("🏆 Quest Master")
            if user_stats.get('trivia_correct', 0) >= 50:
                titles.append("🧠 Lore Keeper")
            if user_stats.get('elemental_fights_victory', 0) >= 25:
                titles.append("⚔️ Elemental Champion")
            if level >= 50:
                titles.append("🌟 Elder Garg")
            
            user_title = " • ".join(titles) if titles else "🥚 Hatchling"
            
            embed = discord.Embed(
                title=f"👤 {interaction.user.display_name}",
                description=f"**{user_title}**",
                color=interaction.user.color or 0x7289DA,
                timestamp=datetime.utcnow()
            )
            embed.set_thumbnail(url=interaction.user.display_avatar.url)
            
            embed.add_field(
                name="📊 Core Stats",
                value=f"**Level:** {level}\n**XP:** {xp:,}\n**Coins:** {user_stats.get('coins', 0):,}",
                inline=True
            )
            
            # Game statistics
            trivia_rate = f"{user_stats.get('trivia_correct', 0)}/{user_stats.get('trivia_total', 0)}"
            fight_wins = user_stats.get('elemental_fights_victory', 0)
            fight_total = user_stats.get('elemental_fights_total', 0)
            fight_rate = f"{fight_wins}/{fight_total}" if fight_total > 0 else "0/0"
            
            embed.add_field(
                name="🎮 Game Stats",
                value=f"**Trivia:** {trivia_rate}\n**Fights Won:** {fight_rate}\n**Daily Quests:** {user_stats.get('daily_quests_completed', 0)}",
                inline=True
            )
            
            embed.add_field(
                name="📈 Progress",
                value=f"**Next Level:** {xp_for_next_level} XP\n**Total Earned:** {user_stats.get('total_coins_earned', user_stats.get('coins', 0)):,} coins",
                inline=True
            )
            
            # Check NFT holder status
            nft_holder = await bot.db.nft_holders.find_one({"user_id": user_id})
            if nft_holder:
                embed.add_field(
                    name="🖼️ NFT Status",
                    value=f"✅ Verified Holder\n**NFTs:** {nft_holder.get('nft_count', 0)}",
                    inline=True
                )
            
            # Calculate rank
            rank_query = await bot.db.user_stats.count_documents({"xp": {"$gt": xp}})
            user_rank = rank_query + 1
            
            embed.add_field(
                name="🏆 Server Rank",
                value=f"**#{user_rank}**",
                inline=True
            )
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Profile error: {e}")
            await interaction.followup.send("❌ Failed to load your mystical profile!")
    
    elif action == "leaderboard":
        try:
            # Get top players
            top_users = await bot.db.user_stats.find().sort("xp", -1).limit(10).to_list(length=10)
            
            if not top_users:
                await interaction.followup.send("📊 The leaderboard is empty! Play some games to claim your spot!")
                return
            
            embed = discord.Embed(
                title="🏆 Lil' Gargs Champions",
                description="*The mightiest Garg masters in the realm*",
                color=0xFFD700,
                timestamp=datetime.utcnow()
            )
            
            leaderboard_text = []
            for i, user_data in enumerate(top_users):
                try:
                    user = bot.get_user(int(user_data['user_id']))
                    username = user.display_name if user else "Unknown Warrior"
                    
                    xp = user_data.get('xp', 0)
                    level = xp // 100
                    coins = user_data.get('coins', 0)
                    
                    if i == 0:
                        medal = "👑"
                    elif i == 1:
                        medal = "🥈"
                    elif i == 2:
                        medal = "🥉"
                    else:
                        medal = f"**{i+1}.**"
                    
                    leaderboard_text.append(f"{medal} **{username}** - Lv.{level} ({xp:,} XP)")
                except:
                    continue
            
            embed.add_field(
                name="⚔️ Top Champions",
                value="\n".join(leaderboard_text) if leaderboard_text else "No champions yet",
                inline=False
            )
            
            # User's position
            user_stats = await bot.db.user_stats.find_one({"user_id": user_id})
            if user_stats:
                user_xp = user_stats.get('xp', 0)
                user_level = user_xp // 100
                rank_query = await bot.db.user_stats.count_documents({"xp": {"$gt": user_xp}})
                user_rank = rank_query + 1
                
                embed.add_field(
                    name="📈 Your Standing",
                    value=f"**Rank:** #{user_rank}\n**Level:** {user_level}\n**XP:** {user_xp:,}",
                    inline=True
                )
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Leaderboard error: {e}")
            await interaction.followup.send("❌ The leaderboard crystals are clouded!")

async def start_trivia_session(interaction, user_id: str, difficulty: str):
    """Start trivia session for button"""
    # Get trivia questions
    trivia_questions = get_trivia_question_pool()
    
    rewards = {
        "easy": {"coins": 15, "xp": 30},
        "medium": {"coins": 25, "xp": 50},
        "hard": {"coins": 40, "xp": 80}
    }
    
    # Get user's trivia history to avoid recent repeats
    user_stats = await bot.db.user_stats.find_one({"user_id": user_id})
    recent_questions = user_stats.get('recent_trivia_questions', []) if user_stats else []
    
    # Filter available questions
    available_questions = []
    for q in trivia_questions[difficulty]:
        if q['question'] not in recent_questions:
            available_questions.append(q)
    
    if not available_questions:
        available_questions = trivia_questions[difficulty]
        recent_questions = []
    
    # Select question
    selected_question = random.choice(available_questions)
    recent_questions.append(selected_question['question'])
    if len(recent_questions) > 5:
        recent_questions = recent_questions[-5:]
    
    # Start session
    await bot.db.user_stats.update_one(
        {"user_id": user_id},
        {
            "$set": {
                "trivia_session": {
                    "active": True,
                    "difficulty": difficulty,
                    "started_at": datetime.utcnow(),
                    "questions_answered": 0,
                    "correct_answers": 0
                },
                "current_trivia": {
                    "question": selected_question['question'],
                    "correct_answer": selected_question['answer'],
                    "difficulty": difficulty,
                    "rewards": rewards[difficulty],
                    "timestamp": datetime.utcnow()
                },
                "recent_trivia_questions": recent_questions
            }
        },
        upsert=True
    )
    
    embed = discord.Embed(
        title=f"🧠 Trivia Started! ({difficulty.title()})",
        description=f"**Question 1:** {selected_question['question']}",
        color=0x7289DA
    )
    
    option_text = '\n'.join(selected_question['options'])
    embed.add_field(name="🎯 Options", value=option_text, inline=False)
    embed.add_field(
        name="🎮 How to Play", 
        value="Reply with **A**, **B**, **C**, or **D**\nType **stop** to end session", 
        inline=False
    )
    
    await interaction.followup.send(embed=embed)

async def execute_elemental_fight(interaction, user_id: str, element: str):
    """Execute elemental fight for button"""
    elements = ["fire", "ice", "nature", "storm", "shadow"]
    enemy_element = random.choice(elements)
    
    element_emojis = {
        "fire": "🔥", "ice": "❄️", "nature": "🌿", "storm": "⚡", "shadow": "🌑"
    }
    
    # Elemental advantages
    advantages = {
        "fire": {"weak_to": ["ice", "storm"], "strong_against": ["nature", "shadow"]},
        "ice": {"weak_to": ["fire", "nature"], "strong_against": ["storm", "shadow"]},
        "nature": {"weak_to": ["fire", "shadow"], "strong_against": ["ice", "storm"]},
        "storm": {"weak_to": ["nature", "ice"], "strong_against": ["fire", "shadow"]},
        "shadow": {"weak_to": ["nature", "storm"], "strong_against": ["fire", "ice"]}
    }
    
    user_power = random.randint(50, 100)
    enemy_power = random.randint(50, 100)
    
    # Apply advantages
    if enemy_element in advantages[element]["strong_against"]:
        user_power += 25
        advantage = f"✅ Your {element_emojis[element]} {element.title()} has advantage!"
    elif enemy_element in advantages[element]["weak_to"]:
        user_power -= 20
        advantage = f"❌ Your {element_emojis[element]} {element.title()} is weak!"
    else:
        advantage = f"⚖️ Neutral matchup"
    
    # Determine winner
    if user_power > enemy_power:
        result = "victory"
        coins_earned = 30
        xp_earned = 60
        result_text = f"🎉 **VICTORY!** You defeated the {enemy_element.title()} guardian!"
        color = 0x00FF00
    else:
        result = "defeat"
        coins_earned = 10
        xp_earned = 20
        result_text = f"💀 **DEFEAT!** The {enemy_element.title()} guardian won!"
        color = 0xFF0000
    
    # Award rewards
    await bot.db.user_stats.update_one(
        {"user_id": user_id},
        {
            "$inc": {
                "coins": coins_earned,
                "xp": xp_earned,
                f"elemental_fights_{result}": 1,
                "elemental_fights_total": 1
            }
        },
        upsert=True
    )
    
    embed = discord.Embed(
        title="⚔️ Elemental Battle Result",
        description=result_text,
        color=color
    )
    
    embed.add_field(
        name="🔮 Battle Details",
        value=f"**Your Power:** {user_power}\n**Enemy Power:** {enemy_power}",
        inline=True
    )
    
    embed.add_field(
        name="🎁 Rewards",
        value=f"**{coins_earned} coins** • **{xp_earned} XP**",
        inline=True
    )
    
    embed.add_field(
        name="⚡ Elemental Effect",
        value=advantage,
        inline=False
    )
    
    await interaction.followup.send(embed=embed)

async def execute_daily_quest(interaction, user_id: str):
    """Execute daily quest for button"""
    today = datetime.utcnow().date()
    user_stats = await bot.db.user_stats.find_one({"user_id": user_id})
    
    last_quest_date = None
    if user_stats and 'last_daily_quest' in user_stats:
        last_quest_date = user_stats['last_daily_quest'].date()
    
    if last_quest_date == today:
        embed = discord.Embed(
            title="✅ Daily Quest Complete",
            description="You've already completed today's quest! Come back tomorrow.",
            color=0x0099FF
        )
        await interaction.followup.send(embed=embed)
        return
    
    # Random quest
    daily_quests = [
        {
            "title": "🔥 Elemental Mastery",
            "description": "You've proven your elemental prowess for today!",
            "reward_coins": 75,
            "reward_xp": 150
        },
        {
            "title": "🧠 Scholar of Gargs",
            "description": "Your knowledge of the Gargs grows stronger!",
            "reward_coins": 60,
            "reward_xp": 120
        },
        {
            "title": "🎲 Fortune's Favor",
            "description": "Lady luck smiles upon you today!",
            "reward_coins": 50,
            "reward_xp": 100
        },
        {
            "title": "🤝 Community Spirit",
            "description": "You've contributed to the community today!",
            "reward_coins": 80,
            "reward_xp": 160
        }
    ]
    
    quest = random.choice(daily_quests)
    
    # Award quest
    await bot.db.user_stats.update_one(
        {"user_id": user_id},
        {
            "$inc": {
                "coins": quest["reward_coins"],
                "xp": quest["reward_xp"],
                "daily_quests_completed": 1
            },
            "$set": {"last_daily_quest": datetime.utcnow()}
        },
        upsert=True
    )
    
    embed = discord.Embed(
        title=quest["title"],
        description=quest["description"],
        color=0xFFD700
    )
    embed.add_field(
        name="🎁 Rewards Earned",
        value=f"**{quest['reward_coins']} coins** • **{quest['reward_xp']} XP**",
        inline=True
    )
    
    await interaction.followup.send(embed=embed)

async def execute_dice_game(interaction, user_id: str, bet: int):
    """Execute dice game for button"""
    # Check coins
    user_stats = await bot.db.user_stats.find_one({"user_id": user_id})
    current_coins = user_stats.get('coins', 0) if user_stats else 0
    
    if current_coins < bet:
        await interaction.followup.send(f"❌ You only have {current_coins} coins! You need {bet} to play.")
        return
    
    # Roll dice
    user_roll = random.randint(1, 6)
    bot_roll = random.randint(1, 6)
    
    # Determine outcome
    if user_roll > bot_roll:
        coins_change = bet
        result = "won"
        color = 0x00FF00
        result_text = f"🎉 **YOU WIN!** Your {user_roll} beats {bot_roll}!"
    elif user_roll < bot_roll:
        coins_change = -bet
        result = "lost"
        color = 0xFF0000
        result_text = f"💸 **YOU LOSE!** Your {user_roll} loses to {bot_roll}!"
    else:
        coins_change = 0
        result = "tied"
        color = 0xFFFF00
        result_text = f"🤝 **TIE!** Both rolled {user_roll}!"
    
    # Update coins
    await bot.db.user_stats.update_one(
        {"user_id": user_id},
        {
            "$inc": {
                "coins": coins_change,
                f"dice_games_{result}": 1,
                "dice_games_total": 1
            }
        },
        upsert=True
    )
    
    embed = discord.Embed(
        title="🎲 Dice Roll Result",
        description=result_text,
        color=color
    )
    
    embed.add_field(
        name="🎯 Rolls",
        value=f"**You:** 🎲 {user_roll}\n**Bot:** 🎲 {bot_roll}",
        inline=True
    )
    
    new_balance = current_coins + coins_change
    embed.add_field(
        name="💰 Coins",
        value=f"**Change:** {coins_change:+d}\n**Balance:** {new_balance}",
        inline=True
    )
    
    await interaction.followup.send(embed=embed)

# ========================
# MESSAGE LISTENER FOR CONTINUOUS TRIVIA AND GAME ANSWERS
# ========================

@bot.event
async def on_message(message):
    """Enhanced message handler for game responses and continuous trivia"""
    if message.author == bot.user:
        return
    
    user_id = str(message.author.id)
    content = message.content.strip().lower()
    
    # Handle continuous trivia system
    user_stats = await bot.db.user_stats.find_one({"user_id": user_id})
    
    # Check for trivia stop command
    if content == "stop" and user_stats and user_stats.get('trivia_session', {}).get('active'):
        await handle_trivia_stop(message, user_stats)
        return
    
    # Check for trivia answers (A, B, C, D)
    if content in ['a', 'b', 'c', 'd'] and user_stats and user_stats.get('current_trivia'):
        await handle_trivia_answer(message, user_stats, content.upper())
        return
    
    # Check for guess game answers (1, 2, 3, 4)
    elif content in ['1', '2', '3', '4'] and user_stats and user_stats.get('current_guess_game'):
        guess_game = user_stats['current_guess_game']
        
        # Check if guess game expired
        if datetime.utcnow() - guess_game['timestamp'] <= timedelta(seconds=30):
            correct_answer = guess_game['correct_answer']
            trait = guess_game['trait']
            
            if content == correct_answer:
                # Correct guess
                await bot.db.user_stats.update_one(
                    {"user_id": user_id},
                    {
                        "$inc": {
                            "coins": 25,
                            "xp": 50,
                            "guess_games_correct": 1,
                            "guess_games_total": 1
                        },
                        "$unset": {"current_guess_game": ""}
                    }
                )
                await message.reply(f"🎉 **Correct!** The Garg had **{trait}**! You earned **25 coins** and **50 XP**!")
            else:
                # Wrong guess
                await bot.db.user_stats.update_one(
                    {"user_id": user_id},
                    {
                        "$inc": {"guess_games_total": 1},
                        "$unset": {"current_guess_game": ""}
                    }
                )
                await message.reply(f"❌ **Wrong!** The Garg had **{trait}**. Try again next time!")
    
    # Check if bot was mentioned for AI responses
    elif bot.user in message.mentions:
        content_clean = message.content.replace(f'<@{bot.user.id}>', '').strip()
        
        if content_clean:
            response = await bot.get_ai_response(
                user_id,
                content_clean,
                context="mention",
                guild_id=str(message.guild.id) if message.guild else None
            )
            await message.reply(response)
    
    # Process commands
    await bot.process_commands(message)

async def handle_trivia_stop(message, user_stats):
    """Handle stopping continuous trivia"""
    session = user_stats.get('trivia_session', {})
    questions_answered = session.get('questions_answered', 0)
    correct_answers = session.get('correct_answers', 0)
    accuracy = int((correct_answers / max(1, questions_answered)) * 100) if questions_answered > 0 else 0
    
    # Clear session
    await bot.db.user_stats.update_one(
        {"user_id": str(message.author.id)},
        {"$unset": {"trivia_session": "", "current_trivia": ""}}
    )
    
    embed = discord.Embed(
        title="🏁 Trivia Session Complete!",
        description="Thanks for playing continuous trivia!",
        color=0x00FF00
    )
    
    embed.add_field(
        name="📊 Final Results",
        value=f"**Questions:** {questions_answered}\n**Correct:** {correct_answers}\n**Accuracy:** {accuracy}%",
        inline=True
    )
    
    if accuracy >= 80 and questions_answered >= 3:
        bonus_coins = 25
        embed.add_field(
            name="🏆 Accuracy Bonus",
            value=f"**+{bonus_coins} coins** for excellent performance!",
            inline=True
        )
        
        await bot.db.user_stats.update_one(
            {"user_id": str(message.author.id)},
            {"$inc": {"coins": bonus_coins}},
            upsert=True
        )
    
    await message.reply(embed=embed)

async def handle_trivia_answer(message, user_stats, answer):
    """Handle trivia answer in continuous mode"""
    trivia = user_stats['current_trivia']
    session = user_stats.get('trivia_session', {})
    
    # Check if trivia expired (60 seconds)
    if datetime.utcnow() - trivia['timestamp'] > timedelta(seconds=60):
        await message.reply("⏰ **Time's up!** Type **stop** to end your trivia session or I'll ask another question!")
        await send_next_trivia_question(message, str(message.author.id), session['difficulty'])
        return
    
    correct_answer = trivia['correct_answer']
    rewards = trivia['rewards']
    is_correct = answer == correct_answer
    
    # Update session stats
    questions_answered = session.get('questions_answered', 0) + 1
    correct_answers = session.get('correct_answers', 0) + (1 if is_correct else 0)
    
    if is_correct:
        # Correct answer
        await bot.db.user_stats.update_one(
            {"user_id": str(message.author.id)},
            {
                "$inc": {
                    "coins": rewards['coins'],
                    "xp": rewards['xp'],
                    "trivia_correct": 1,
                    "trivia_total": 1
                },
                "$set": {
                    "trivia_session.questions_answered": questions_answered,
                    "trivia_session.correct_answers": correct_answers
                }
            }
        )
        
        embed = discord.Embed(
            title="🎉 Correct!",
            description=f"Great job! You earned **{rewards['coins']} coins** and **{rewards['xp']} XP**!",
            color=0x00FF00
        )
        embed.add_field(name="Answer", value=f"✅ {correct_answer}", inline=True)
        embed.add_field(name="Progress", value=f"**{correct_answers}/{questions_answered}** correct", inline=True)
        
    else:
        # Wrong answer
        await bot.db.user_stats.update_one(
            {"user_id": str(message.author.id)},
            {
                "$inc": {"trivia_total": 1},
                "$set": {
                    "trivia_session.questions_answered": questions_answered,
                    "trivia_session.correct_answers": correct_answers
                }
            }
        )
        
        embed = discord.Embed(
            title="❌ Incorrect",
            description="Better luck with the next question!",
            color=0xFF0000
        )
        embed.add_field(name="Correct Answer", value=f"✅ {correct_answer}", inline=True)
        embed.add_field(name="Your Answer", value=f"❌ {answer}", inline=True)
        embed.add_field(name="Progress", value=f"**{correct_answers}/{questions_answered}** correct", inline=True)
    
    embed.set_footer(text="Next question coming up... Type 'stop' to end session")
    await message.reply(embed=embed)
    
    # Send next question after 3 seconds
    await asyncio.sleep(3)
    await send_next_trivia_question(message, str(message.author.id), session['difficulty'])

async def send_next_trivia_question(message, user_id: str, difficulty: str):
    """Send the next question in continuous trivia"""
    try:
        # Get trivia questions (same as in game command)
        trivia_questions = get_trivia_question_pool()
        
        # Get user's recent questions to avoid repeats
        user_stats = await bot.db.user_stats.find_one({"user_id": user_id})
        recent_questions = user_stats.get('recent_trivia_questions', []) if user_stats else []
        
        # Filter available questions
        available_questions = []
        for q in trivia_questions[difficulty]:
            if q['question'] not in recent_questions:
                available_questions.append(q)
        
        if not available_questions:
            available_questions = trivia_questions[difficulty]
            recent_questions = []
        
        # Select and store question
        selected_question = random.choice(available_questions)
        recent_questions.append(selected_question['question'])
        if len(recent_questions) > 5:
            recent_questions = recent_questions[-5:]
        
        rewards = {
            "easy": {"coins": 15, "xp": 30},
            "medium": {"coins": 25, "xp": 50},
            "hard": {"coins": 40, "xp": 80}
        }
        
        # Update database
        await bot.db.user_stats.update_one(
            {"user_id": user_id},
            {
                "$set": {
                    "current_trivia": {
                        "question": selected_question['question'],
                        "correct_answer": selected_question['answer'],
                        "difficulty": difficulty,
                        "rewards": rewards[difficulty],
                        "timestamp": datetime.utcnow()
                    },
                    "recent_trivia_questions": recent_questions
                }
            }
        )
        
        session = user_stats.get('trivia_session', {})
        question_num = session.get('questions_answered', 0) + 1
        
        embed = discord.Embed(
            title=f"🧠 Question {question_num} ({difficulty.title()})",
            description=selected_question['question'],
            color=0x7289DA
        )
        
        option_text = '\n'.join(selected_question['options'])
        embed.add_field(name="🎯 Options", value=option_text, inline=False)
        embed.add_field(
            name="🎁 Reward", 
            value=f"**{rewards[difficulty]['coins']} coins** • **{rewards[difficulty]['xp']} XP**", 
            inline=True
        )
        embed.set_footer(text="Reply with A, B, C, or D • Type 'stop' to end")
        
        await message.channel.send(embed=embed)
        
    except Exception as e:
        logger.error(f"Next trivia question error: {e}")
        await message.channel.send("❌ Error loading next question. Type **stop** to end session.")

def get_trivia_question_pool():
    """Get the trivia question pool (extracted for reuse)"""
    return {
        "easy": [
            {
                "question": "What blockchain is the Lil' Gargs collection built on?",
                "options": ["A) Ethereum", "B) Solana", "C) Polygon", "D) Cardano"],
                "answer": "B"
            },
            {
                "question": "Which of these is NOT a Lil' Gargs element?",
                "options": ["A) Fire", "B) Water", "C) Ice", "D) Nature"],
                "answer": "B"
            },
            {
                "question": "What type of creatures are Lil' Gargs?",
                "options": ["A) Dragons", "B) Gargoyles", "C) Demons", "D) Angels"],
                "answer": "B"
            },
            {
                "question": "How many elements are in the Lil' Gargs universe?",
                "options": ["A) 4", "B) 5", "C) 6", "D) 7"],
                "answer": "B"
            },
            {
                "question": "What does NFT stand for?",
                "options": ["A) New File Type", "B) Non-Fungible Token", "C) Network Function Tool", "D) No Fun Today"],
                "answer": "B"
            },
            {
                "question": "Which personality type is known for being brave and daring?",
                "options": ["A) Calm", "B) Clever", "C) Bold", "D) Wild"],
                "answer": "C"
            }
        ],
        "medium": [
            {
                "question": "What is the Lil' Gargs verified creator address?",
                "options": ["A) 9fT6Spqbv9FxK7Ktxr6bDfASWc6k5acUNr1zMv5WrGfA", "B) FP2bGBGHWrW4w82hsSDGc5zNLQ83CvEmW2shGkttS7aZ", "C) DYw8jCTfwHNRJhhmFcbXvVDTqWMEVFBX6ZKUmG5CNSKK", "D) 11111111111111111111111111111112"],
                "answer": "A"
            },
            {
                "question": "In elemental battles, what element has advantage over Nature?",
                "options": ["A) Ice", "B) Storm", "C) Fire", "D) Shadow"],
                "answer": "C"
            },
            {
                "question": "What storage system is used for Lil' Gargs metadata?",
                "options": ["A) AWS S3", "B) Google Drive", "C) IPFS", "D) Dropbox"],
                "answer": "C"
            },
            {
                "question": "Which element is weak against Ice?",
                "options": ["A) Fire", "B) Nature", "C) Storm", "D) Shadow"],
                "answer": "A"
            },
            {
                "question": "What happens when pets reach 100 XP?",
                "options": ["A) They evolve", "B) They level up", "C) They get sick", "D) Nothing"],
                "answer": "B"
            },
            {
                "question": "How often can you feed your Lil' Garg pet?",
                "options": ["A) Every hour", "B) Every 2 hours", "C) Every 3 hours", "D) Once per day"],
                "answer": "B"
            }
        ],
        "hard": [
            {
                "question": "What is the Lil' Gargs contract address?",
                "options": ["A) 9fT6Spqbv9FxK7Ktxr6bDfASWc6k5acUNr1zMv5WrGfA", "B) FP2bGBGHWrW4w82hsSDGc5zNLQ83CvEmW2shGkttS7aZ", "C) DYw8jCTfwHNRJhhmFcbXvVDTqWMEVFBX6ZKUmG5CNSKK", "D) bafybeif32gaqsngxdaply6x5m5htxpuuxw2dljvdv6iokek3xod7lmus24"],
                "answer": "B"
            },
            {
                "question": "In the elemental advantage system, which element combination creates a neutral matchup?",
                "options": ["A) Fire vs Fire", "B) Ice vs Nature", "C) Storm vs Shadow", "D) All of the above"],
                "answer": "D"
            },
            {
                "question": "What is the adoption cost for a new Lil' Garg pet?",
                "options": ["A) 50 coins", "B) 75 coins", "C) 100 coins", "D) 150 coins"],
                "answer": "C"
            },
            {
                "question": "Which API service is used for Solana NFT verification?",
                "options": ["A) Metaplex", "B) Helius", "C) Alchemy", "D) QuickNode"],
                "answer": "B"
            },
            {
                "question": "How many stat points do pets gain per level on average?",
                "options": ["A) 2-5 per stat", "B) 5-10 per stat", "C) 1-3 per stat", "D) 10-15 per stat"],
                "answer": "A"
            },
            {
                "question": "What is the minimum bet amount for dice games?",
                "options": ["A) 1 coin", "B) 5 coins", "C) 10 coins", "D) 25 coins"],
                "answer": "B"
            }
        ]
    }

# ========================
# CONFIGURATION SYSTEM
# ========================

@bot.tree.command(name="config", description="Configure bot settings and place interactive buttons")
@app_commands.describe(
    action="Configuration action",
    channel="Channel to configure",
    role="Role to configure", 
    amount="Amount threshold for NFT roles",
    message="Custom message for welcome system"
)
@app_commands.choices(action=[
    app_commands.Choice(name="petbutton", value="petbutton"),
    app_commands.Choice(name="gamebutton", value="gamebutton"),
    app_commands.Choice(name="battlebutton", value="battlebutton"),
    app_commands.Choice(name="verifybutton", value="verifybutton"),
    app_commands.Choice(name="ticketbutton", value="ticketbutton"),
    app_commands.Choice(name="nftroles", value="nftroles"),
    app_commands.Choice(name="welcome", value="welcome"),
    app_commands.Choice(name="channels", value="channels"),
    app_commands.Choice(name="view", value="view")
])
async def config_command(interaction: discord.Interaction, action: str, channel: discord.TextChannel = None, 
                        role: discord.Role = None, amount: int = None, message: str = None):
    """Enhanced configuration system for Lil' Gargs bot"""
    
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("❌ You need administrator permissions to use configuration commands!", ephemeral=True)
        return
    
    await interaction.response.defer()
    
    guild_id = str(interaction.guild.id)
    
    try:
        if action == "petbutton":
            if not channel:
                await interaction.followup.send("❌ Please specify a channel for the pet adoption button!")
                return
            
            embed = discord.Embed(
                title="🐾 Adopt Your Lil' Garg Pet",
                description="Ready to welcome a magical companion into your life? Adopt your very own Lil' Garg pet and begin an epic adventure!",
                color=0x00FF00
            )
            
            embed.add_field(
                name="🌟 What You Get",
                value="• A unique pet with element & personality\n• Care, train, and battle with your Garg\n• Level up and grow stronger together\n• Unlock special abilities and moves",
                inline=False
            )
            
            embed.add_field(
                name="💰 Adoption Cost",
                value="**100 coins** (earn coins through games!)",
                inline=True
            )
            
            embed.add_field(
                name="🎮 Getting Started",
                value="Click the button below to adopt your pet, then use `/pet status` to check on them!",
                inline=False
            )
            
            class FunctionalPetButton(discord.ui.View):
                def __init__(self):
                    super().__init__(timeout=None)
                
                @discord.ui.button(label='🐾 Adopt Pet', style=discord.ButtonStyle.green, custom_id='functional_adopt_pet')
                async def adopt_pet(self, interaction: discord.Interaction, button: discord.ui.Button):
                    """Actually adopt a pet via button"""
                    await interaction.response.defer(ephemeral=True)
                    
                    user_id = str(interaction.user.id)
                    
                    # Check if user already has a pet
                    existing_pet = await bot.db.pets.find_one({"owner_id": user_id})
                    if existing_pet:
                        await interaction.followup.send("❌ You already have a Lil' Garg pet! Use `/pet status` to check on them.", ephemeral=True)
                        return
                    
                    # Check coins
                    user_stats = await bot.db.user_stats.find_one({"user_id": user_id})
                    coins = user_stats.get('coins', 0) if user_stats else 0
                    
                    adoption_cost = 100
                    if coins < adoption_cost:
                        await interaction.followup.send(f"❌ You need {adoption_cost} coins to adopt a pet! You have {coins} coins.\nPlay games to earn more coins!", ephemeral=True)
                        return
                    
                    # Generate pet data
                    elements = ["fire", "ice", "nature", "storm", "shadow"]
                    personalities = ["bold", "calm", "clever", "wild"]
                    
                    element = random.choice(elements)
                    personality = random.choice(personalities)
                    
                    element_emojis = {
                        "fire": "🔥", "ice": "❄️", "nature": "🌿", "storm": "⚡", "shadow": "🌑"
                    }
                    
                    # Generate random name
                    name_prefixes = ["Spark", "Frost", "Leaf", "Storm", "Shadow", "Ember", "Crystal", "Vine", "Thunder", "Mystic"]
                    name_suffixes = ["wing", "claw", "heart", "eye", "tail", "horn", "scale", "breath", "roar", "whisper"]
                    pet_name = f"{random.choice(name_prefixes)}{random.choice(name_suffixes)}"
                    
                    # Create pet
                    pet_data = {
                        "pet_id": str(uuid.uuid4()),
                        "owner_id": user_id,
                        "name": pet_name,
                        "element": element,
                        "personality": personality,
                        "level": 1,
                        "xp": 0,
                        "attack": random.randint(10, 20),
                        "defense": random.randint(10, 20),
                        "health": 100,
                        "max_health": 100,
                        "mood": 75,
                        "energy": 100,
                        "last_fed": datetime.utcnow() - timedelta(hours=1),
                        "last_trained": datetime.utcnow() - timedelta(hours=2),
                        "last_played": datetime.utcnow() - timedelta(hours=1),
                        "created_at": datetime.utcnow(),
                        "total_battles": 0,
                        "battles_won": 0
                    }
                    
                    await bot.db.pets.insert_one(pet_data)
                    
                    # Deduct coins
                    await bot.db.user_stats.update_one(
                        {"user_id": user_id},
                        {"$inc": {"coins": -adoption_cost}},
                        upsert=True
                    )
                    
                    embed = discord.Embed(
                        title="🎉 Pet Adopted Successfully!",
                        description=f"Welcome **{pet_name}**, your new {element_emojis[element]} {element.title()} Garg!",
                        color=0x00FF00
                    )
                    
                    embed.add_field(
                        name="🧬 Your Pet's Traits",
                        value=f"**Element:** {element_emojis[element]} {element.title()}\n**Personality:** {personality.title()}",
                        inline=True
                    )
                    
                    embed.add_field(
                        name="⚔️ Base Stats",
                        value=f"**Attack:** {pet_data['attack']}\n**Defense:** {pet_data['defense']}\n**Health:** {pet_data['health']}",
                        inline=True
                    )
                    
                    embed.add_field(
                        name="🎮 Next Steps",
                        value="Use `/pet status` to check on your pet!\nTry `/pet feed`, `/pet play`, and `/pet train`",
                        inline=False
                    )
                    
                    embed.set_footer(text=f"Adoption cost: {adoption_cost} coins")
                    
                    await interaction.followup.send(embed=embed, ephemeral=True)
            
            view = FunctionalPetButton()
            await channel.send(embed=embed, view=view)
            
            await interaction.followup.send(f"✅ **Functional pet adoption button** placed in {channel.mention}!")
        
        elif action == "gamebutton":
            if not channel:
                await interaction.followup.send("❌ Please specify a channel for the game hub button!")
                return
            
            embed = discord.Embed(
                title="🎮 Lil' Gargs Game Hub",
                description="Test your knowledge, battle the elements, and climb the leaderboards in the ultimate Garg gaming experience!",
                color=0x7289DA
            )
            
            embed.add_field(
                name="🎯 Available Games",
                value="• **Trivia** - Continuous knowledge challenges\n• **Elemental Fights** - Battle with the elements\n• **Daily Quests** - Complete daily challenges\n• **Dice Games** - Risk coins for big rewards\n• **Guess the Garg** - Identify mysterious traits",
                inline=False
            )
            
            embed.add_field(
                name="🏆 Earn Rewards",
                value="Gain **coins**, **XP**, and **achievements**!\nClimb the leaderboards and earn titles!",
                inline=False
            )
            
            class SimpleGameButton(discord.ui.View):
                def __init__(self):
                    super().__init__(timeout=None)
                
                @discord.ui.button(label='🎮 Play Games', style=discord.ButtonStyle.primary, custom_id='simple_game_hub')
                async def play_games(self, interaction: discord.Interaction, button: discord.ui.Button):
                    embed = discord.Embed(
                        title="🎮 Game Commands",
                        description="Choose your game:",
                        color=0x7289DA
                    )
                    
                    embed.add_field(
                        name="🧠 Trivia Games",
                        value="`/game trivia [difficulty]` - Start continuous trivia\n`/submitquestion` - Add your questions",
                        inline=False
                    )
                    
                    embed.add_field(
                        name="⚔️ Combat Games", 
                        value="`/game elemental-fight [element]` - Elemental battles\n`/game dice [bet]` - Risk coins on rolls",
                        inline=False
                    )
                    
                    embed.add_field(
                        name="🎯 Other Games",
                        value="`/game dailyquest` - Daily tasks\n`/game guessgarg` - Guess NFT traits\n`/game profile` - Your stats\n`/game leaderboard` - Rankings",
                        inline=False
                    )
                    
                    await interaction.response.send_message(embed=embed, ephemeral=True)
            
            view = SimpleGameButton()
            await channel.send(embed=embed, view=view)
            
            await interaction.followup.send(f"✅ Game hub button placed in {channel.mention}!")
        
        elif action == "battlebutton":
            if not channel:
                await interaction.followup.send("❌ Please specify a channel for the battle arena button!")
                return
            
            embed = discord.Embed(
                title="⚔️ Battle Arena",
                description="Challenge other pet owners to epic turn-based battles! Test your strategy and elemental mastery.",
                color=0xFF0000
            )
            
            embed.add_field(
                name="🏟️ How It Works",
                value="• Challenge other users to pet battles\n• Use strategy and elemental advantages\n• Choose from Attack, Defend, or Special moves\n• Win battles to earn coins and XP\n• Climb the battle rankings",
                inline=False
            )
            
            embed.add_field(
                name="🎁 Battle Rewards",
                value="**Winner:** 100 coins + 150 XP\n**Participant:** 25 coins + 50 XP\n**Pet XP:** Bonus for both pets",
                inline=False
            )
            
            class SimpleBattleButton(discord.ui.View):
                def __init__(self):
                    super().__init__(timeout=None)
                
                @discord.ui.button(label='⚔️ Enter Arena', style=discord.ButtonStyle.red, custom_id='simple_battle_arena')
                async def enter_arena(self, interaction: discord.Interaction, button: discord.ui.Button):
                    await interaction.response.send_message(
                        "⚔️ **Welcome to the Battle Arena!**\n\n" +
                        "**Commands:**\n" +
                        "• `/battle start @user` - Challenge someone\n" +
                        "• `/battle accept code:[CODE]` - Accept challenges\n" +
                        "• `/battle arena` - View active battles\n" +
                        "• `/battle profile` - Your battle stats\n\n" +
                        "💡 **Tip:** Make sure your pet is healthy before battling!",
                        ephemeral=True
                    )
            
            view = SimpleBattleButton()
            await channel.send(embed=embed, view=view)
            
            await interaction.followup.send(f"✅ Battle arena button placed in {channel.mention}!")
        
        elif action == "verifybutton":
            if not channel:
                await interaction.followup.send("❌ Please specify a channel for the wallet verification button!")
                return
            
            embed = discord.Embed(
                title="🖼️ Verify Your Lil' Gargs NFTs",
                description="Connect your Solana wallet to verify your Lil' Gargs ownership and unlock exclusive perks!",
                color=0x9932CC
            )
            
            embed.add_field(
                name="✨ Verification Benefits",
                value="• Get verified holder roles\n• Access to exclusive channels\n• Special privileges and features\n• Show off your NFT collection\n• Automatic role assignment based on holdings",
                inline=False
            )
            
            embed.add_field(
                name="🔒 Privacy & Security",
                value="We only check for Lil' Gargs NFTs. Your wallet remains completely secure.",
                inline=False
            )
            
            class SimpleVerifyButton(discord.ui.View):
                def __init__(self):
                    super().__init__(timeout=None)
                
                @discord.ui.button(label='🖼️ Verify Wallet', style=discord.ButtonStyle.secondary, custom_id='simple_verify_wallet')
                async def verify_wallet(self, interaction: discord.Interaction, button: discord.ui.Button):
                    await interaction.response.send_message(
                        "🖼️ **Ready to verify your wallet?**\n\n" +
                        "Use `/nft verify [wallet_address]` with your Solana wallet address.\n\n" +
                        "**Example:** `/nft verify DYw8jCTfwHNRJhhmFcbXvVDTqWMEVFBX6ZKUmG5CNSKK`\n\n" +
                        "🔒 **Safe & Secure** - We only check for Lil' Gargs NFTs!",
                        ephemeral=True
                    )
            
            view = SimpleVerifyButton()
            await channel.send(embed=embed, view=view)
            
            await interaction.followup.send(f"✅ Wallet verification button placed in {channel.mention}!")
        
        elif action == "ticketbutton":
            if not channel:
                await interaction.followup.send("❌ Please specify a channel for the ticket button!")
                return
            
            embed = discord.Embed(
                title="🎫 Support Tickets",
                description="Need help? Have a question? Click the button below to open a private support ticket!",
                color=0x0099FF
            )
            
            embed.add_field(
                name="📝 How it works:",
                value="• Click **📩 Open Ticket** below\n• A private channel will be created for you\n• Describe your issue or question\n• Our support team will assist you\n• Close the ticket when resolved",
                inline=False
            )
            
            embed.add_field(
                name="🚀 What we can help with:",
                value="• Technical issues\n• Account questions\n• Bot feature support\n• Community guidance\n• General inquiries",
                inline=False
            )
            
            embed.set_footer(text="Lil' Gargs Support System • Click below to get started!")
            
            # Use the existing TicketView
            view = TicketView()
            await channel.send(embed=embed, view=view)
            
            await interaction.followup.send(f"✅ Ticket support button placed in {channel.mention}!")
        
        elif action == "nftroles":
            if not role or amount is None:
                await interaction.followup.send("❌ Please specify both a role and NFT amount threshold!\n**Example:** `/config nftroles role:@Whale amount:5`")
                return
            
            # Store NFT role configuration
            await bot.db.server_configs.update_one(
                {"guild_id": guild_id},
                {
                    "$push": {
                        "nft_roles": {
                            "role_id": str(role.id),
                            "role_name": role.name,
                            "nft_threshold": amount,
                            "created_at": datetime.utcnow()
                        }
                    }
                },
                upsert=True
            )
            
            await interaction.followup.send(f"✅ **NFT Role Configured!**\n{role.mention} will be given to users with **{amount}+ Lil' Gargs NFTs**")
        
        elif action == "welcome":
            if not message:
                await interaction.followup.send("❌ Please provide a custom welcome message!\n**Example:** `/config welcome message:Welcome to our awesome Lil' Gargs community!`")
                return
            
            await bot.db.server_configs.update_one(
                {"guild_id": guild_id},
                {"$set": {"welcome_message": message, "welcome_enabled": True}},
                upsert=True
            )
            
            await interaction.followup.send(f"✅ **Welcome message configured!**\nNew members will receive: *{message[:100]}{'...' if len(message) > 100 else ''}*")
        
        elif action == "view":
            # Show current configuration
            config = await bot.db.server_configs.find_one({"guild_id": guild_id})
            
            embed = discord.Embed(
                title="⚙️ Server Configuration",
                description="Current bot settings for this server",
                color=0x0099FF
            )
            
            if config:
                # Show NFT roles
                if 'nft_roles' in config and config['nft_roles']:
                    role_list = []
                    for nft_role in config['nft_roles']:
                        role_obj = interaction.guild.get_role(int(nft_role['role_id']))
                        role_mention = role_obj.mention if role_obj else f"@{nft_role['role_name']} (deleted)"
                        role_list.append(f"{role_mention} - {nft_role['nft_threshold']}+ NFTs")
                    
                    embed.add_field(
                        name="🖼️ NFT Roles",
                        value="\n".join(role_list),
                        inline=False
                    )
                
                # Show welcome message
                if config.get('welcome_message'):
                    welcome_status = "✅ Enabled" if config.get('welcome_enabled') else "❌ Disabled"
                    embed.add_field(
                        name=f"👋 Welcome Message {welcome_status}",
                        value=config['welcome_message'][:150] + "..." if len(config['welcome_message']) > 150 else config['welcome_message'],
                        inline=False
                    )
                
                # Show other configs
                other_configs = []
                if config.get('support_role_id'):
                    support_role = interaction.guild.get_role(int(config['support_role_id']))
                    other_configs.append(f"**Support Role:** {support_role.mention if support_role else 'Not found'}")
                
                if config.get('ticket_log_channel_id'):
                    log_channel = interaction.guild.get_channel(int(config['ticket_log_channel_id']))
                    other_configs.append(f"**Ticket Logs:** {log_channel.mention if log_channel else 'Not found'}")
                
                if config.get('mod_log_channel_id'):
                    mod_channel = interaction.guild.get_channel(int(config['mod_log_channel_id']))
                    other_configs.append(f"**Mod Logs:** {mod_channel.mention if mod_channel else 'Not found'}")
                
                if other_configs:
                    embed.add_field(
                        name="🔧 Other Settings",
                        value="\n".join(other_configs),
                        inline=False
                    )
            else:
                embed.description = "No configuration found. Use `/config` commands to set up features."
            
            embed.add_field(
                name="📋 Available Commands",
                value="• `/config petbutton #channel` - Pet adoption button\n• `/config gamebutton #channel` - Game hub button\n• `/config battlebutton #channel` - Battle arena button\n• `/config verifybutton #channel` - Wallet verification button\n• `/config ticketbutton #channel` - Support ticket button\n• `/config nftroles role:@Role amount:5` - NFT-based roles\n• `/config welcome message:text` - Welcome message",
                inline=False
            )
            
            await interaction.followup.send(embed=embed)
        
        else:
            await interaction.followup.send("❌ Unknown configuration action!")
    
    except Exception as e:
        logger.error(f"Config command error: {e}")
        await interaction.followup.send("❌ Configuration failed! Please try again.")

# ========================
# WELCOME SYSTEM
# ========================

@bot.event
async def on_member_join(member):
    """Handle new member welcome with AI"""
    try:
        config = await bot.db.server_configs.find_one({"guild_id": str(member.guild.id)})
        
        # Check if welcome is enabled
        if not config or not config.get('welcome_enabled', True):
            return
        
        if config and config.get('welcome_message'):
            # Use custom welcome message
            welcome_text = config['welcome_message']
        else:
            # Generate AI welcome message
            welcome_prompt = f"""Create a warm, personalized welcome message for {member.display_name} who just joined the Lil' Gargs Discord server. 

Include:
- Welcome them to the community
- Brief explanation of Lil' Gargs (cute gargoyle NFTs on Solana with elements)
- Mention they can verify their wallet if they own NFTs (/nft verify)
- Suggest adopting a pet to get started (/pet adopt)
- Mention playing games to earn coins (/game trivia)
- Keep it friendly, enthusiastic but concise (under 300 words)

Make it personal and engaging for the Lil' Gargs universe!"""
            
            welcome_text = await bot.get_ai_response("system", welcome_prompt, context="welcome")
        
        # Send welcome DM
        try:
            embed = discord.Embed(
                title=f"👋 Welcome to {member.guild.name}!",
                description=welcome_text,
                color=0x00FF00,
                timestamp=datetime.utcnow()
            )
            
            embed.add_field(
                name="🚀 Getting Started",
                value="• Use `/nft verify [wallet]` if you own Lil' Gargs\n• Try `/pet adopt [name]` to get a companion\n• Play `/game trivia` to earn coins\n• Join battles with `/battle start @user`",
                inline=False
            )
            
            embed.add_field(
                name="🎮 Quick Commands",
                value="• `/help` - See all commands\n• `/game profile` - Your stats\n• `/pet status` - Check your pet\n• `/config view` - Server settings (admin)",
                inline=False
            )
            
            embed.set_footer(text="Enjoy your time in the Lil' Gargs community!")
            
            await member.send(embed=embed)
            logger.info(f"Welcome DM sent to {member.display_name}")
            
        except discord.Forbidden:
            # If DM fails, try to send in a welcome channel
            welcome_channel = discord.utils.get(member.guild.channels, name="welcome") or discord.utils.get(member.guild.channels, name="general")
            if welcome_channel:
                embed = discord.Embed(
                    title="👋 New Member!",
                    description=f"Welcome {member.mention} to the Lil' Gargs community!",
                    color=0x00FF00
                )
                embed.add_field(
                    name="💌 Check Your DMs",
                    value="I tried to send you a welcome message with getting started info!",
                    inline=False
                )
                await welcome_channel.send(embed=embed)
                logger.info(f"Welcome channel message sent for {member.display_name}")
        
        # Auto-assign roles if they have NFTs verified
        await check_and_assign_nft_roles(member, config)
        
    except Exception as e:
        logger.error(f"Welcome system error: {e}")

async def check_and_assign_nft_roles(member, config):
    """Check if member has verified NFTs and assign appropriate roles"""
    try:
        if not config or 'nft_roles' not in config:
            return
        
        # Check if user has verified NFTs
        nft_holder = await bot.db.nft_holders.find_one({"user_id": str(member.id)})
        if not nft_holder:
            return
        
        nft_count = nft_holder.get('nft_count', 0)
        roles_to_add = []
        
        for nft_role_config in config['nft_roles']:
            if nft_count >= nft_role_config['nft_threshold']:
                role = member.guild.get_role(int(nft_role_config['role_id']))
                if role and role not in member.roles:
                    roles_to_add.append(role)
        
        if roles_to_add:
            await member.add_roles(*roles_to_add, reason=f"Auto-assigned for {nft_count} verified NFTs")
            logger.info(f"Auto-assigned NFT roles to {member.display_name}: {[r.name for r in roles_to_add]}")
    
    except Exception as e:
        logger.error(f"Auto role assignment error: {e}")

# ========================
# ENHANCED MESSAGE LISTENER FOR GAME ANSWERS
# ========================

@bot.tree.command(name="submitquestion", description="Submit a trivia question for review")
@app_commands.describe(
    difficulty="Question difficulty level",
    question="The trivia question",
    option_a="Option A",
    option_b="Option B", 
    option_c="Option C",
    option_d="Option D",
    correct_answer="Correct answer (A, B, C, or D)"
)
@app_commands.choices(difficulty=[
    app_commands.Choice(name="easy", value="easy"),
    app_commands.Choice(name="medium", value="medium"),
    app_commands.Choice(name="hard", value="hard")
])
async def submit_question(interaction: discord.Interaction, difficulty: str, question: str, 
                         option_a: str, option_b: str, option_c: str, option_d: str, correct_answer: str):
    """Submit a community trivia question for review"""
    await interaction.response.defer(ephemeral=True)
    
    # Validate correct answer
    if correct_answer.upper() not in ['A', 'B', 'C', 'D']:
        await interaction.followup.send("❌ Correct answer must be A, B, C, or D!", ephemeral=True)
        return
    
    try:
        # Store the submission
        submission = {
            "submission_id": str(uuid.uuid4()),
            "submitted_by": str(interaction.user.id),
            "submitter_name": interaction.user.display_name,
            "guild_id": str(interaction.guild.id),
            "difficulty": difficulty,
            "question": question,
            "options": [f"A) {option_a}", f"B) {option_b}", f"C) {option_c}", f"D) {option_d}"],
            "correct_answer": correct_answer.upper(),
            "status": "pending",
            "submitted_at": datetime.utcnow()
        }
        
        await bot.db.trivia_submissions.insert_one(submission)
        
        embed = discord.Embed(
            title="📝 Question Submitted!",
            description="Your trivia question has been submitted for admin review.",
            color=0x00FF00,
            timestamp=datetime.utcnow()
        )
        
        embed.add_field(
            name="📋 Your Submission",
            value=f"**Difficulty:** {difficulty.title()}\n**Question:** {question[:100]}{'...' if len(question) > 100 else ''}",
            inline=False
        )
        
        embed.add_field(
            name="⏳ What's Next?",
            value="Admins will review your question and it may be added to the trivia pool. Thanks for contributing!",
            inline=False
        )
        
        await interaction.followup.send(embed=embed, ephemeral=True)
        
        # Notify admins if there's a submission log channel configured
        server_config = await bot.db.server_configs.find_one({"guild_id": str(interaction.guild.id)})
        if server_config and server_config.get('trivia_log_channel_id'):
            try:
                log_channel = interaction.guild.get_channel(int(server_config['trivia_log_channel_id']))
                if log_channel:
                    admin_embed = discord.Embed(
                        title="📝 New Trivia Submission",
                        description=f"**Submitted by:** {interaction.user.mention}\n**Difficulty:** {difficulty.title()}",
                        color=0x0099FF,
                        timestamp=datetime.utcnow()
                    )
                    
                    admin_embed.add_field(name="❓ Question", value=question, inline=False)
                    options_text = f"{option_a}\n{option_b}\n{option_c}\n{option_d}"
                    admin_embed.add_field(name="📝 Options", value=options_text, inline=True)
                    admin_embed.add_field(name="✅ Answer", value=correct_answer.upper(), inline=True)
                    admin_embed.add_field(name="🆔 Submission ID", value=submission['submission_id'][:8], inline=True)
                    
                    await log_channel.send(embed=admin_embed)
            except:
                pass
                
    except Exception as e:
        logger.error(f"Submit question error: {e}")
        await interaction.followup.send("❌ Failed to submit question. Please try again!", ephemeral=True)

# ========================
# SLASH COMMANDS - PET SYSTEM
# ========================

@bot.tree.command(name="pet", description="Your Lil' Garg pet companion")
@app_commands.describe(
    action="Choose a pet action",
    pet_name="Name for your pet (for adopt/rename)",
    item="Item to use or buy"
)
@app_commands.choices(action=[
    app_commands.Choice(name="adopt", value="adopt"),
    app_commands.Choice(name="status", value="status"),
    app_commands.Choice(name="feed", value="feed"),
    app_commands.Choice(name="train", value="train"),
    app_commands.Choice(name="play", value="play"),
    app_commands.Choice(name="rename", value="rename"),
    app_commands.Choice(name="shop", value="shop")
])
async def pet_command(interaction: discord.Interaction, action: str, pet_name: str = None, item: str = None):
    """Complete Lil' Gargs pet system"""
    await interaction.response.defer()
    
    user_id = str(interaction.user.id)
    
    if action == "adopt":
        try:
            # Check if user already has a pet
            existing_pet = await bot.db.pets.find_one({"owner_id": user_id})
            if existing_pet:
                await interaction.followup.send("❌ You already have a Lil' Garg pet! Use `/pet status` to check on them.")
                return
            
            # Check if user has enough coins
            user_stats = await bot.db.user_stats.find_one({"user_id": user_id})
            coins = user_stats.get('coins', 0) if user_stats else 0
            
            adoption_cost = 100
            if coins < adoption_cost:
                await interaction.followup.send(f"❌ You need {adoption_cost} coins to adopt a pet! You have {coins} coins.\nPlay games to earn more!")
                return
            
            # Generate random pet
            elements = ["fire", "ice", "nature", "storm", "shadow"]
            personalities = ["bold", "calm", "clever", "wild"]
            
            element = random.choice(elements)
            personality = random.choice(personalities)
            
            element_emojis = {
                "fire": "🔥", "ice": "❄️", "nature": "🌿", "storm": "⚡", "shadow": "🌑"
            }
            
            # Generate pet name if not provided
            if not pet_name:
                name_prefixes = ["Spark", "Frost", "Leaf", "Storm", "Shadow", "Ember", "Crystal", "Vine", "Thunder", "Mystic"]
                name_suffixes = ["wing", "claw", "heart", "eye", "tail", "horn", "scale", "breath", "roar", "whisper"]
                pet_name = f"{random.choice(name_prefixes)}{random.choice(name_suffixes)}"
            
            # Create pet
            pet_data = {
                "pet_id": str(uuid.uuid4()),
                "owner_id": user_id,
                "name": pet_name,
                "element": element,
                "personality": personality,
                "level": 1,
                "xp": 0,
                "attack": random.randint(10, 20),
                "defense": random.randint(10, 20),
                "health": 100,
                "max_health": 100,
                "mood": 75,
                "energy": 100,
                "last_fed": datetime.utcnow() - timedelta(hours=1),
                "last_trained": datetime.utcnow() - timedelta(hours=2),
                "last_played": datetime.utcnow() - timedelta(hours=1),
                "created_at": datetime.utcnow(),
                "total_battles": 0,
                "battles_won": 0
            }
            
            await bot.db.pets.insert_one(pet_data)
            
            # Deduct coins
            await bot.db.user_stats.update_one(
                {"user_id": user_id},
                {"$inc": {"coins": -adoption_cost}},
                upsert=True
            )
            
            embed = discord.Embed(
                title="🥚 Pet Adoption Successful!",
                description=f"Welcome **{pet_name}**, the {element_emojis[element]} {element.title()} Garg!",
                color=0x00FF00,
                timestamp=datetime.utcnow()
            )
            
            embed.add_field(
                name="🧬 Traits",
                value=f"**Element:** {element_emojis[element]} {element.title()}\n**Personality:** {personality.title()}",
                inline=True
            )
            
            embed.add_field(
                name="⚔️ Stats",
                value=f"**Attack:** {pet_data['attack']}\n**Defense:** {pet_data['defense']}\n**Health:** {pet_data['health']}",
                inline=True
            )
            
            embed.add_field(
                name="😊 Condition",
                value=f"**Mood:** {pet_data['mood']}%\n**Energy:** {pet_data['energy']}%",
                inline=True
            )
            
            embed.add_field(
                name="📝 Getting Started",
                value="Use `/pet feed`, `/pet play`, and `/pet train` to care for your new companion!",
                inline=False
            )
            
            embed.set_footer(text=f"Adoption cost: {adoption_cost} coins")
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Pet adopt error: {e}")
            await interaction.followup.send("❌ The pet adoption center is closed. Try again later!")
    
    elif action == "status":
        try:
            pet = await bot.db.pets.find_one({"owner_id": user_id})
            if not pet:
                await interaction.followup.send("❌ You don't have a pet! Use `/pet adopt` to get your first Lil' Garg companion.")
                return
            
            # Calculate time since last interactions
            now = datetime.utcnow()
            time_since_fed = now - pet['last_fed']
            time_since_trained = now - pet['last_trained']
            time_since_played = now - pet['last_played']
            
            # Update mood based on neglect
            mood_decay = 0
            if time_since_fed > timedelta(hours=6):
                mood_decay += 10
            if time_since_played > timedelta(hours=8):
                mood_decay += 15
            if time_since_trained > timedelta(hours=12):
                mood_decay += 5
            
            current_mood = max(0, pet['mood'] - mood_decay)
            
            # Update pet in database if mood changed
            if mood_decay > 0:
                await bot.db.pets.update_one(
                    {"owner_id": user_id},
                    {"$set": {"mood": current_mood}}
                )
            
            element_emojis = {
                "fire": "🔥", "ice": "❄️", "nature": "🌿", "storm": "⚡", "shadow": "🌑"
            }
            
            # Determine mood emoji
            if current_mood >= 80:
                mood_emoji = "😄"
                mood_desc = "Very Happy"
            elif current_mood >= 60:
                mood_emoji = "😊"
                mood_desc = "Happy"
            elif current_mood >= 40:
                mood_emoji = "😐"
                mood_desc = "Neutral"
            elif current_mood >= 20:
                mood_emoji = "😞"
                mood_desc = "Sad"
            else:
                mood_emoji = "😢"
                mood_desc = "Very Sad"
            
            embed = discord.Embed(
                title=f"{element_emojis[pet['element']]} {pet['name']}",
                description=f"*A {pet['personality']} {pet['element'].title()} Garg*",
                color=0x7289DA,
                timestamp=datetime.utcnow()
            )
            
            embed.add_field(
                name="📊 Stats",
                value=f"**Level:** {pet['level']}\n**XP:** {pet['xp']}/100\n**Attack:** {pet['attack']}\n**Defense:** {pet['defense']}",
                inline=True
            )
            
            embed.add_field(
                name="❤️ Health",
                value=f"**HP:** {pet['health']}/{pet['max_health']}\n**Energy:** {pet['energy']}%",
                inline=True
            )
            
            embed.add_field(
                name=f"{mood_emoji} Mood",
                value=f"**{mood_desc}** ({current_mood}%)",
                inline=True
            )
            
            # Battle stats
            win_rate = f"{pet['battles_won']}/{pet['total_battles']}" if pet['total_battles'] > 0 else "0/0"
            embed.add_field(
                name="⚔️ Battle Record",
                value=f"**Wins:** {win_rate}",
                inline=True
            )
            
            # Care status
            care_status = []
            if time_since_fed < timedelta(hours=3):
                care_status.append("🍽️ Well Fed")
            elif time_since_fed < timedelta(hours=6):
                care_status.append("🍽️ Getting Hungry")
            else:
                care_status.append("🍽️ Very Hungry!")
            
            if time_since_played < timedelta(hours=4):
                care_status.append("🎾 Entertained")
            elif time_since_played < timedelta(hours=8):
                care_status.append("🎾 Bored")
            else:
                care_status.append("🎾 Very Bored!")
            
            embed.add_field(
                name="🏠 Care Status",
                value="\n".join(care_status),
                inline=True
            )
            
            # Generate mood-based message
            if current_mood >= 80:
                messages = [
                    f"{pet['name']} is radiating with joy!",
                    f"{pet['name']} purrs contentedly.",
                    f"{pet['name']} does a little happy dance!"
                ]
            elif current_mood >= 60:
                messages = [
                    f"{pet['name']} looks pleased.",
                    f"{pet['name']} nuzzles you affectionately.",
                    f"{pet['name']} wags their tail."
                ]
            elif current_mood >= 40:
                messages = [
                    f"{pet['name']} seems okay.",
                    f"{pet['name']} looks at you expectantly.",
                    f"{pet['name']} stretches lazily."
                ]
            elif current_mood >= 20:
                messages = [
                    f"{pet['name']} looks a bit down.",
                    f"{pet['name']} sighs quietly.",
                    f"{pet['name']} needs some attention."
                ]
            else:
                messages = [
                    f"{pet['name']} looks very unhappy.",
                    f"{pet['name']} whimpers softly.",
                    f"{pet['name']} really needs care!"
                ]
            
            embed.add_field(
                name="💭 Current Mood",
                value=random.choice(messages),
                inline=False
            )
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Pet status error: {e}")
            await interaction.followup.send("❌ Your pet is hiding! Try again later.")
    
    elif action == "feed":
        try:
            pet = await bot.db.pets.find_one({"owner_id": user_id})
            if not pet:
                await interaction.followup.send("❌ You don't have a pet to feed! Use `/pet adopt` first.")
                return
            
            # Check cooldown (can feed every 2 hours)
            time_since_fed = datetime.utcnow() - pet['last_fed']
            if time_since_fed < timedelta(hours=2):
                wait_time = timedelta(hours=2) - time_since_fed
                hours, remainder = divmod(wait_time.seconds, 3600)
                minutes = remainder // 60
                await interaction.followup.send(f"❌ {pet['name']} isn't hungry yet! Try again in {hours}h {minutes}m.")
                return
            
            # Feed the pet
            mood_gain = random.randint(10, 20)
            health_gain = random.randint(5, 15)
            xp_gain = random.randint(3, 8)
            
            new_mood = min(100, pet['mood'] + mood_gain)
            new_health = min(pet['max_health'], pet['health'] + health_gain)
            new_xp = pet['xp'] + xp_gain
            
            # Check for level up
            new_level = pet['level']
            if new_xp >= 100:
                new_level += 1
                new_xp = 0
                stat_gain_attack = random.randint(2, 5)
                stat_gain_defense = random.randint(2, 5)
                stat_gain_health = random.randint(10, 20)
                
                await bot.db.pets.update_one(
                    {"owner_id": user_id},
                    {
                        "$set": {
                            "mood": new_mood,
                            "health": new_health,
                            "xp": new_xp,
                            "level": new_level,
                            "last_fed": datetime.utcnow()
                        },
                        "$inc": {
                            "attack": stat_gain_attack,
                            "defense": stat_gain_defense,
                            "max_health": stat_gain_health
                        }
                    }
                )
                
                embed = discord.Embed(
                    title="🍽️ Feeding Complete!",
                    description=f"**{pet['name']} enjoyed the meal!**\n\n🎉 **LEVEL UP!** {pet['name']} reached level {new_level}!",
                    color=0xFFD700,
                    timestamp=datetime.utcnow()
                )
                
                embed.add_field(
                    name="📈 Level Up Gains",
                    value=f"**Attack:** +{stat_gain_attack}\n**Defense:** +{stat_gain_defense}\n**Max Health:** +{stat_gain_health}",
                    inline=True
                )
            else:
                await bot.db.pets.update_one(
                    {"owner_id": user_id},
                    {
                        "$set": {
                            "mood": new_mood,
                            "health": new_health,
                            "xp": new_xp,
                            "last_fed": datetime.utcnow()
                        }
                    }
                )
                
                embed = discord.Embed(
                    title="🍽️ Feeding Complete!",
                    description=f"**{pet['name']} enjoyed the meal!**",
                    color=0x00FF00,
                    timestamp=datetime.utcnow()
                )
            
            embed.add_field(
                name="📊 Gains",
                value=f"**Mood:** +{mood_gain}% ({new_mood}%)\n**Health:** +{health_gain} HP ({new_health}/{pet['max_health']})\n**XP:** +{xp_gain} ({new_xp}/100)",
                inline=True
            )
            
            feeding_messages = [
                f"{pet['name']} devours the food happily!",
                f"{pet['name']} savors every bite.",
                f"{pet['name']} purrs while eating.",
                f"{pet['name']} looks grateful for the meal."
            ]
            
            embed.add_field(
                name="💭 Pet Response",
                value=random.choice(feeding_messages),
                inline=False
            )
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Pet feed error: {e}")
            await interaction.followup.send("❌ The food bowl is empty! Try again later.")
    
    elif action == "train":
        try:
            pet = await bot.db.pets.find_one({"owner_id": user_id})
            if not pet:
                await interaction.followup.send("❌ You don't have a pet to train! Use `/pet adopt` first.")
                return
            
            # Check cooldown (can train every 3 hours)
            time_since_trained = datetime.utcnow() - pet['last_trained']
            if time_since_trained < timedelta(hours=3):
                wait_time = timedelta(hours=3) - time_since_trained
                hours, remainder = divmod(wait_time.seconds, 3600)
                minutes = remainder // 60
                await interaction.followup.send(f"❌ {pet['name']} is too tired to train! Try again in {hours}h {minutes}m.")
                return
            
            # Check energy (need at least 30 energy to train)
            if pet['energy'] < 30:
                await interaction.followup.send(f"❌ {pet['name']} doesn't have enough energy to train! Let them rest or play to restore energy.")
                return
            
            # Training session
            xp_gain = random.randint(8, 15)
            energy_cost = random.randint(20, 35)
            stat_choice = random.choice(['attack', 'defense'])
            stat_gain = random.randint(1, 3)
            
            new_xp = pet['xp'] + xp_gain
            new_energy = max(0, pet['energy'] - energy_cost)
            
            # Check for level up
            new_level = pet['level']
            level_up_bonus = {}
            if new_xp >= 100:
                new_level += 1
                new_xp = 0
                level_up_bonus = {
                    "attack": random.randint(3, 6),
                    "defense": random.randint(3, 6),
                    "max_health": random.randint(15, 25)
                }
            
            # Update pet
            update_data = {
                "xp": new_xp,
                "energy": new_energy,
                "level": new_level,
                "last_trained": datetime.utcnow()
            }
            update_data[stat_choice] = pet[stat_choice] + stat_gain + level_up_bonus.get(stat_choice, 0)
            
            if level_up_bonus:
                for stat, bonus in level_up_bonus.items():
                    if stat != stat_choice:
                        update_data[stat] = pet[stat] + bonus
            
            await bot.db.pets.update_one(
                {"owner_id": user_id},
                {"$set": update_data}
            )
            
            embed = discord.Embed(
                title="💪 Training Complete!",
                description=f"**{pet['name']} completed a rigorous training session!**",
                color=0x0099FF,
                timestamp=datetime.utcnow()
            )
            
            if new_level > pet['level']:
                embed.description += f"\n\n🎉 **LEVEL UP!** {pet['name']} reached level {new_level}!"
                embed.color = 0xFFD700
                
                gains_text = f"**{stat_choice.title()}:** +{stat_gain + level_up_bonus.get(stat_choice, 0)}\n"
                for stat, bonus in level_up_bonus.items():
                    if stat != stat_choice:
                        gains_text += f"**{stat.replace('_', ' ').title()}:** +{bonus}\n"
                gains_text += f"**XP:** +{xp_gain}\n**Energy:** -{energy_cost}"
                
                embed.add_field(
                    name="📈 Total Gains",
                    value=gains_text,
                    inline=True
                )
            else:
                embed.add_field(
                    name="📊 Training Results",
                    value=f"**{stat_choice.title()}:** +{stat_gain}\n**XP:** +{xp_gain} ({new_xp}/100)\n**Energy:** -{energy_cost} ({new_energy}%)",
                    inline=True
                )
            
            training_messages = [
                f"{pet['name']} pushes through the challenging exercises!",
                f"{pet['name']} shows great determination!",
                f"{pet['name']} is getting stronger!",
                f"{pet['name']} focuses intensely on training."
            ]
            
            embed.add_field(
                name="💭 Training Session",
                value=random.choice(training_messages),
                inline=False
            )
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Pet train error: {e}")
            await interaction.followup.send("❌ The training grounds are closed! Try again later.")
    
    elif action == "play":
        try:
            pet = await bot.db.pets.find_one({"owner_id": user_id})
            if not pet:
                await interaction.followup.send("❌ You don't have a pet to play with! Use `/pet adopt` first.")
                return
            
            # Check cooldown (can play every 1 hour)
            time_since_played = datetime.utcnow() - pet['last_played']
            if time_since_played < timedelta(hours=1):
                wait_time = timedelta(hours=1) - time_since_played
                minutes = wait_time.seconds // 60
                await interaction.followup.send(f"❌ {pet['name']} is resting! Try again in {minutes} minutes.")
                return
            
            # Play session
            mood_gain = random.randint(15, 25)
            energy_gain = random.randint(10, 20)
            xp_gain = random.randint(2, 6)
            
            new_mood = min(100, pet['mood'] + mood_gain)
            new_energy = min(100, pet['energy'] + energy_gain)
            new_xp = pet['xp'] + xp_gain
            
            # Check for level up
            new_level = pet['level']
            if new_xp >= 100:
                new_level += 1
                new_xp = 0
            
            await bot.db.pets.update_one(
                {"owner_id": user_id},
                {
                    "$set": {
                        "mood": new_mood,
                        "energy": new_energy,
                        "xp": new_xp,
                        "level": new_level,
                        "last_played": datetime.utcnow()
                    }
                }
            )
            
            play_activities = [
                "fetch", "tug-of-war", "hide and seek", "flying practice", "treasure hunt", 
                "elemental games", "riddle solving", "acrobatics", "dancing", "storytelling"
            ]
            
            activity = random.choice(play_activities)
            
            embed = discord.Embed(
                title="🎾 Playtime Complete!",
                description=f"**{pet['name']} had a blast playing {activity}!**",
                color=0xFF69B4,
                timestamp=datetime.utcnow()
            )
            
            if new_level > pet['level']:
                embed.description += f"\n\n🎉 **LEVEL UP!** {pet['name']} reached level {new_level}!"
                embed.color = 0xFFD700
            
            embed.add_field(
                name="😊 Play Results",
                value=f"**Mood:** +{mood_gain}% ({new_mood}%)\n**Energy:** +{energy_gain}% ({new_energy}%)\n**XP:** +{xp_gain} ({new_xp}/100)",
                inline=True
            )
            
            play_messages = [
                f"{pet['name']} bounces with joy!",
                f"{pet['name']} purrs with contentment!",
                f"{pet['name']} does a happy little dance!",
                f"{pet['name']} nuzzles you affectionately!"
            ]
            
            embed.add_field(
                name="💭 Pet Response",
                value=random.choice(play_messages),
                inline=False
            )
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Pet play error: {e}")
            await interaction.followup.send("❌ The toys are missing! Try again later.")
    
    elif action == "rename":
        if not pet_name:
            await interaction.followup.send("❌ Please provide a new name for your pet!")
            return
        
        if len(pet_name) > 20:
            await interaction.followup.send("❌ Pet name must be 20 characters or less!")
            return
        
        try:
            pet = await bot.db.pets.find_one({"owner_id": user_id})
            if not pet:
                await interaction.followup.send("❌ You don't have a pet to rename! Use `/pet adopt` first.")
                return
            
            old_name = pet['name']
            
            await bot.db.pets.update_one(
                {"owner_id": user_id},
                {"$set": {"name": pet_name}}
            )
            
            embed = discord.Embed(
                title="📝 Pet Renamed!",
                description=f"**{old_name}** is now known as **{pet_name}**!",
                color=0x00FF00,
                timestamp=datetime.utcnow()
            )
            
            embed.add_field(
                name="💭 Pet Response",
                value=f"{pet_name} seems pleased with their new name!",
                inline=False
            )
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Pet rename error: {e}")
            await interaction.followup.send("❌ The name registry is down! Try again later.")
    
    elif action == "shop":
        try:
            embed = discord.Embed(
                title="🛍️ Pet Shop",
                description="*Welcome to the Lil' Gargs Pet Emporium!*",
                color=0x9932CC,
                timestamp=datetime.utcnow()
            )
            
            shop_items = [
                {"name": "🍎 Premium Food", "price": 25, "description": "Restores more health and mood when feeding"},
                {"name": "🎾 Energy Toy", "price": 30, "description": "Gives extra energy when playing"},
                {"name": "💪 Training Weights", "price": 50, "description": "Increases stat gains from training"},
                {"name": "❤️ Health Potion", "price": 40, "description": "Instantly restores pet to full health"},
                {"name": "⚡ Energy Drink", "price": 35, "description": "Instantly restores pet to full energy"},
                {"name": "🏆 XP Boost", "price": 60, "description": "Doubles XP gain for next 3 activities"}
            ]
            
            shop_text = ""
            for item in shop_items:
                shop_text += f"**{item['name']}** - {item['price']} coins\n*{item['description']}*\n\n"
            
            embed.add_field(
                name="🛒 Available Items",
                value=shop_text,
                inline=False
            )
            
            embed.add_field(
                name="💡 How to Buy",
                value="Use `/pet shop item:[item_name]` to purchase items!\nExample: `/pet shop item:Premium Food`",
                inline=False
            )
            
            # Show user's coins
            user_stats = await bot.db.user_stats.find_one({"user_id": user_id})
            coins = user_stats.get('coins', 0) if user_stats else 0
            
            embed.add_field(
                name="💰 Your Balance",
                value=f"{coins} coins",
                inline=True
            )
            
            embed.set_footer(text="Items will be automatically used when needed!")
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"Pet shop error: {e}")
            await interaction.followup.send("❌ The pet shop is closed! Try again later.")

# ========================
# SLASH COMMANDS - MODERATION SYSTEM
# ========================

@bot.tree.command(name="purge", description="Delete multiple messages")
@app_commands.describe(amount="Number of messages to delete (1-100)")
async def purge(interaction: discord.Interaction, amount: int):
    """Purge messages from the channel"""
    if not interaction.user.guild_permissions.manage_messages:
        await interaction.response.send_message("❌ You need Manage Messages permission to use this command!", ephemeral=True)
        return
    
    if amount < 1 or amount > 100:
        await interaction.response.send_message("❌ Amount must be between 1 and 100!", ephemeral=True)
        return
    
    await interaction.response.defer()
    
    try:
        deleted = await interaction.channel.purge(limit=amount)
        
        embed = discord.Embed(
            title="🗑️ Messages Purged",
            description=f"Successfully deleted **{len(deleted)}** messages",
            color=0x0099FF,
            timestamp=datetime.utcnow()
        )
        embed.add_field(name="Moderator", value=interaction.user.mention, inline=True)
        embed.add_field(name="Channel", value=interaction.channel.mention, inline=True)
        
        # Send confirmation (will auto-delete)
        await interaction.followup.send(embed=embed, delete_after=5)
        
        # Log to mod log
        await log_moderation_action(
            bot, interaction.guild, "PURGE", interaction.user,
            f"Purged {len(deleted)} messages in {interaction.channel.mention}"
        )
        
    except Exception as e:
        logger.error(f"Purge error: {e}")
        await interaction.followup.send("❌ Failed to purge messages! Check bot permissions.")

@bot.tree.command(name="ban", description="Ban a user from the server")
@app_commands.describe(
    user="User to ban",
    reason="Reason for the ban",
    delete_days="Days of messages to delete (0-7)"
)
async def ban_user(interaction: discord.Interaction, user: discord.Member, reason: str = "No reason provided", delete_days: int = 0):
    """Ban a user from the server"""
    if not interaction.user.guild_permissions.ban_members:
        await interaction.response.send_message("❌ You need Ban Members permission to use this command!", ephemeral=True)
        return
    
    if delete_days < 0 or delete_days > 7:
        await interaction.response.send_message("❌ Delete days must be between 0 and 7!", ephemeral=True)
        return
    
    # Check role hierarchy
    if user.top_role >= interaction.user.top_role:
        await interaction.response.send_message("❌ You cannot ban someone with equal or higher role!", ephemeral=True)
        return
    
    if user.top_role >= interaction.guild.me.top_role:
        await interaction.response.send_message("❌ I cannot ban someone with equal or higher role than me!", ephemeral=True)
        return
    
    await interaction.response.defer()
    
    try:
        # Try to DM user before banning
        try:
            dm_embed = discord.Embed(
                title="🚫 You have been banned",
                description=f"You have been banned from **{interaction.guild.name}**",
                color=0xFF0000,
                timestamp=datetime.utcnow()
            )
            dm_embed.add_field(name="Reason", value=reason, inline=False)
            dm_embed.add_field(name="Banned by", value=interaction.user.display_name, inline=True)
            
            await user.send(embed=dm_embed)
        except:
            pass  # User might have DMs disabled
        
        # Ban the user
        await user.ban(reason=reason, delete_message_days=delete_days)
        
        embed = discord.Embed(
            title="🚫 User Banned",
            description=f"**{user.display_name}** has been banned",
            color=0xFF0000,
            timestamp=datetime.utcnow()
        )
        embed.add_field(name="User", value=f"{user.mention} ({user.id})", inline=True)
        embed.add_field(name="Moderator", value=interaction.user.mention, inline=True)
        embed.add_field(name="Reason", value=reason, inline=False)
        
        if delete_days > 0:
            embed.add_field(name="Messages Deleted", value=f"{delete_days} day(s)", inline=True)
        
        await interaction.followup.send(embed=embed)
        
        # Log to mod log
        await log_moderation_action(
            bot, interaction.guild, "BAN", interaction.user,
            f"Banned {user.mention} ({user.id}) - Reason: {reason}"
        )
        
    except Exception as e:
        logger.error(f"Ban error: {e}")
        await interaction.followup.send("❌ Failed to ban user! Check bot permissions.")

@bot.tree.command(name="kick", description="Kick a user from the server")
@app_commands.describe(
    user="User to kick",
    reason="Reason for the kick"
)
async def kick_user(interaction: discord.Interaction, user: discord.Member, reason: str = "No reason provided"):
    """Kick a user from the server"""
    if not interaction.user.guild_permissions.kick_members:
        await interaction.response.send_message("❌ You need Kick Members permission to use this command!", ephemeral=True)
        return
    
    # Check role hierarchy
    if user.top_role >= interaction.user.top_role:
        await interaction.response.send_message("❌ You cannot kick someone with equal or higher role!", ephemeral=True)
        return
    
    if user.top_role >= interaction.guild.me.top_role:
        await interaction.response.send_message("❌ I cannot kick someone with equal or higher role than me!", ephemeral=True)
        return
    
    await interaction.response.defer()
    
    try:
        # Try to DM user before kicking
        try:
            dm_embed = discord.Embed(
                title="👢 You have been kicked",
                description=f"You have been kicked from **{interaction.guild.name}**",
                color=0xFF8C00,
                timestamp=datetime.utcnow()
            )
            dm_embed.add_field(name="Reason", value=reason, inline=False)
            dm_embed.add_field(name="Kicked by", value=interaction.user.display_name, inline=True)
            
            await user.send(embed=dm_embed)
        except:
            pass
        
        # Kick the user
        await user.kick(reason=reason)
        
        embed = discord.Embed(
            title="👢 User Kicked",
            description=f"**{user.display_name}** has been kicked",
            color=0xFF8C00,
            timestamp=datetime.utcnow()
        )
        embed.add_field(name="User", value=f"{user.mention} ({user.id})", inline=True)
        embed.add_field(name="Moderator", value=interaction.user.mention, inline=True)
        embed.add_field(name="Reason", value=reason, inline=False)
        
        await interaction.followup.send(embed=embed)
        
        # Log to mod log
        await log_moderation_action(
            bot, interaction.guild, "KICK", interaction.user,
            f"Kicked {user.mention} ({user.id}) - Reason: {reason}"
        )
        
    except Exception as e:
        logger.error(f"Kick error: {e}")
        await interaction.followup.send("❌ Failed to kick user! Check bot permissions.")

@bot.tree.command(name="timeout", description="Timeout a user")
@app_commands.describe(
    user="User to timeout",
    duration="Duration in minutes (max 40320 = 28 days)",
    reason="Reason for the timeout"
)
async def timeout_user(interaction: discord.Interaction, user: discord.Member, duration: int, reason: str = "No reason provided"):
    """Timeout a user"""
    if not interaction.user.guild_permissions.moderate_members:
        await interaction.response.send_message("❌ You need Moderate Members permission to use this command!", ephemeral=True)
        return
    
    if duration < 1 or duration > 40320:  # Max 28 days
        await interaction.response.send_message("❌ Duration must be between 1 and 40320 minutes (28 days)!", ephemeral=True)
        return
    
    # Check role hierarchy
    if user.top_role >= interaction.user.top_role:
        await interaction.response.send_message("❌ You cannot timeout someone with equal or higher role!", ephemeral=True)
        return
    
    await interaction.response.defer()
    
    try:
        # Calculate timeout end time
        timeout_until = datetime.utcnow() + timedelta(minutes=duration)
        
        # Apply timeout
        await user.timeout(timeout_until, reason=reason)
        
        embed = discord.Embed(
            title="⏰ User Timed Out",
            description=f"**{user.display_name}** has been timed out",
            color=0xFFFF00,
            timestamp=datetime.utcnow()
        )
        embed.add_field(name="User", value=f"{user.mention} ({user.id})", inline=True)
        embed.add_field(name="Moderator", value=interaction.user.mention, inline=True)
        embed.add_field(name="Duration", value=f"{duration} minute(s)", inline=True)
        embed.add_field(name="Until", value=f"<t:{int(timeout_until.timestamp())}:F>", inline=True)
        embed.add_field(name="Reason", value=reason, inline=False)
        
        await interaction.followup.send(embed=embed)
        
        # Log to mod log
        await log_moderation_action(
            bot, interaction.guild, "TIMEOUT", interaction.user,
            f"Timed out {user.mention} ({user.id}) for {duration} minutes - Reason: {reason}"
        )
        
    except Exception as e:
        logger.error(f"Timeout error: {e}")
        await interaction.followup.send("❌ Failed to timeout user! Check bot permissions.")

@bot.tree.command(name="slowmode", description="Set channel slowmode")
@app_commands.describe(
    seconds="Slowmode delay in seconds (0-21600)",
    channel="Channel to set slowmode (defaults to current)"
)
async def slowmode(interaction: discord.Interaction, seconds: int, channel: discord.TextChannel = None):
    """Set slowmode for a channel"""
    if not interaction.user.guild_permissions.manage_channels:
        await interaction.response.send_message("❌ You need Manage Channels permission to use this command!", ephemeral=True)
        return
    
    if seconds < 0 or seconds > 21600:
        await interaction.response.send_message("❌ Slowmode must be between 0 and 21600 seconds (6 hours)!", ephemeral=True)
        return
    
    target_channel = channel or interaction.channel
    
    await interaction.response.defer()
    
    try:
        await target_channel.edit(slowmode_delay=seconds)
        
        if seconds == 0:
            description = "Slowmode disabled"
            color = 0x00FF00
        else:
            description = f"Slowmode set to **{seconds}** second(s)"
            color = 0x0099FF
        
        embed = discord.Embed(
            title="🐌 Slowmode Updated",
            description=description,
            color=color,
            timestamp=datetime.utcnow()
        )
        embed.add_field(name="Channel", value=target_channel.mention, inline=True)
        embed.add_field(name="Moderator", value=interaction.user.mention, inline=True)
        
        await interaction.followup.send(embed=embed)
        
        # Log to mod log
        action = "SLOWMODE DISABLED" if seconds == 0 else f"SLOWMODE SET ({seconds}s)"
        await log_moderation_action(
            bot, interaction.guild, action, interaction.user,
            f"Set slowmode to {seconds}s in {target_channel.mention}"
        )
        
    except Exception as e:
        logger.error(f"Slowmode error: {e}")
        await interaction.followup.send("❌ Failed to set slowmode! Check bot permissions.")

@bot.tree.command(name="modlog", description="Set moderation log channel (Admin only)")
@app_commands.describe(channel="Channel to log moderation actions")
async def set_mod_log(interaction: discord.Interaction, channel: discord.TextChannel):
    """Set the moderation log channel"""
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("❌ You need administrator permissions to set the mod log channel!", ephemeral=True)
        return
    
    guild_id = str(interaction.guild.id)
    
    # Update server configuration
    await bot.db.server_configs.update_one(
        {"guild_id": guild_id},
        {"$set": {"mod_log_channel_id": str(channel.id)}},
        upsert=True
    )
    
    await interaction.response.send_message(f"✅ Moderation logs will be sent to {channel.mention}", ephemeral=True)

async def log_moderation_action(bot_instance, guild, action, moderator, details):
    """Log moderation actions to the configured channel"""
    try:
        server_config = await bot_instance.db.server_configs.find_one({"guild_id": str(guild.id)})
        if server_config and server_config.get('mod_log_channel_id'):
            log_channel = guild.get_channel(int(server_config['mod_log_channel_id']))
            if log_channel:
                embed = discord.Embed(
                    title=f"🛡️ {action}",
                    description=details,
                    color=0xFF6B6B,
                    timestamp=datetime.utcnow()
                )
                embed.add_field(name="Moderator", value=moderator.mention, inline=True)
                embed.set_footer(text=f"Moderator ID: {moderator.id}")
                
                await log_channel.send(embed=embed)
    except Exception as e:
        logger.error(f"Failed to log moderation action: {e}")

# ========================
# SLASH COMMANDS - TICKET SYSTEM
# ========================

@bot.tree.command(name="ticket", description="Ticket system commands")
@app_commands.describe(
    action="Choose a ticket action",
    channel="Channel for ticket setup (admin only)",
    role="Support role to mention (admin only)"
)
@app_commands.choices(action=[
    app_commands.Choice(name="setup", value="setup"),
    app_commands.Choice(name="close", value="close"),
    app_commands.Choice(name="config", value="config")
])
async def ticket_command(interaction: discord.Interaction, action: str, channel: discord.TextChannel = None, role: discord.Role = None):
    """Ticket system management commands"""
    
    if action == "setup":
        # Admin only command
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message("❌ You need administrator permissions to set up tickets!", ephemeral=True)
            return
        
        target_channel = channel or interaction.channel
        
        # Create ticket setup embed
        embed = discord.Embed(
            title="🎫 Support Tickets",
            description="Need help? Click the button below to open a private support ticket!",
            color=0x0099FF
        )
        embed.add_field(
            name="📝 How it works:",
            value="• Click **📩 Open Ticket**\n• A private channel will be created\n• Describe your issue\n• Staff will assist you\n• Close when resolved",
            inline=False
        )
        embed.set_footer(text="Lil' Gargs Support System")
        
        # Send embed with ticket creation button
        view = TicketView()
        await target_channel.send(embed=embed, view=view)
        
        await interaction.response.send_message(f"✅ Ticket system set up in {target_channel.mention}!", ephemeral=True)
    
    elif action == "close":
        # Close current ticket (if in ticket channel)
        if not interaction.channel.name.startswith('ticket-'):
            await interaction.response.send_message("❌ This command can only be used in ticket channels!", ephemeral=True)
            return
        
        # Check permissions (ticket creator or admin)
        channel_name = interaction.channel.name
        # Extract username from channel name (ticket-username format)
        if channel_name.startswith('ticket-'):
            ticket_username = channel_name[7:]  # Remove 'ticket-' prefix
            # Check if current user is the ticket creator or admin
            user_clean_name = ''.join(c.lower() for c in interaction.user.display_name if c.isalnum() or c in '-_')[:20]
            if not user_clean_name:
                user_clean_name = f"user{interaction.user.id}"
            
            if not (ticket_username == user_clean_name or interaction.user.guild_permissions.manage_channels):
                await interaction.response.send_message("❌ You can only close your own ticket or need manage channels permission!", ephemeral=True)
                return
        
        await interaction.response.defer()
        
        # Update ticket in database
        await bot.db.tickets.update_one(
            {"channel_id": str(interaction.channel.id)},
            {
                "$set": {
                    "status": "closed",
                    "closed_at": datetime.utcnow(),
                    "closed_by": str(interaction.user.id)
                }
            }
        )
        
        embed = discord.Embed(
            title="🔒 Ticket Closed",
            description=f"This ticket has been closed by {interaction.user.mention}",
            color=0xFF0000,
            timestamp=datetime.utcnow()
        )
        embed.add_field(
            name="📝 Note",
            value="This channel will be deleted in 10 seconds.",
            inline=False
        )
        
        await interaction.followup.send(embed=embed)
        
        # Log ticket closure
        server_config = await bot.db.server_configs.find_one({"guild_id": str(interaction.guild.id)})
        if server_config and server_config.get('ticket_log_channel_id'):
            try:
                log_channel = interaction.guild.get_channel(int(server_config['ticket_log_channel_id']))
                if log_channel:
                    log_embed = discord.Embed(
                        title="🔒 Ticket Closed",
                        description=f"**Channel:** {interaction.channel.name}\n**Closed by:** {interaction.user.mention}",
                        color=0xFF0000,
                        timestamp=datetime.utcnow()
                    )
                    await log_channel.send(embed=log_embed)
            except:
                pass
        
        # Delete channel after delay
        await asyncio.sleep(10)
        await interaction.channel.delete(reason=f"Ticket closed by {interaction.user.display_name}")
    
    elif action == "config":
        # Admin configuration
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message("❌ You need administrator permissions to configure tickets!", ephemeral=True)
            return
        
        guild_id = str(interaction.guild.id)
        
        # Update server configuration
        config_update = {}
        if role:
            config_update['support_role_id'] = str(role.id)
        
        if config_update:
            await bot.db.server_configs.update_one(
                {"guild_id": guild_id},
                {"$set": config_update},
                upsert=True
            )
        
        # Get current config
        config = await bot.db.server_configs.find_one({"guild_id": guild_id}) or {}
        
        embed = discord.Embed(
            title="⚙️ Ticket Configuration",
            color=0x0099FF
        )
        
        support_role_id = config.get('support_role_id')
        support_role_mention = f"<@&{support_role_id}>" if support_role_id else "Not set"
        
        embed.add_field(
            name="🛡️ Support Role",
            value=support_role_mention,
            inline=True
        )
        
        log_channel_id = config.get('ticket_log_channel_id')
        log_channel_mention = f"<#{log_channel_id}>" if log_channel_id else "Not set"
        
        embed.add_field(
            name="📋 Log Channel",
            value=log_channel_mention,
            inline=True
        )
        
        if role:
            embed.add_field(
                name="✅ Updated",
                value=f"Support role set to {role.mention}",
                inline=False
            )
        
        await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.tree.command(name="ticketlog", description="Set ticket log channel (Admin only)")
@app_commands.describe(channel="Channel to log ticket events")
async def ticket_log(interaction: discord.Interaction, channel: discord.TextChannel):
    """Set the ticket logging channel"""
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("❌ You need administrator permissions to configure ticket logging!", ephemeral=True)
        return
    
    guild_id = str(interaction.guild.id)
    
    # Update server configuration
    await bot.db.server_configs.update_one(
        {"guild_id": guild_id},
        {"$set": {"ticket_log_channel_id": str(channel.id)}},
        upsert=True
    )
    
    await interaction.response.send_message(f"✅ Ticket logs will be sent to {channel.mention}", ephemeral=True)

# ========================
# SLASH COMMANDS - NFT SYSTEM
# ========================

@bot.tree.command(name="nft", description="NFT-related commands")
@app_commands.describe(
    action="Choose an NFT action",
    wallet="Solana wallet address (for verify action)",
    token_id="NFT token ID (for flex action)"
)
@app_commands.choices(action=[
    app_commands.Choice(name="verify", value="verify"),
    app_commands.Choice(name="flex", value="flex"),
    app_commands.Choice(name="stats", value="stats")
])
async def nft_command(interaction: discord.Interaction, action: str, wallet: str = None, token_id: str = None):
    """NFT verification and display commands"""
    await interaction.response.defer()
    
    if action == "verify":
        if not wallet:
            embed = discord.Embed(
                title="❌ Wallet Address Required",
                description="Please provide your Solana wallet address to verify your Lil' Gargs NFTs.",
                color=0xFF0000
            )
            embed.add_field(
                name="📝 Example",
                value="`/nft verify DYw8jCTfwHNRJhhmFcbXvVDTqWMEVFBX6ZKUmG5CNSKK`",
                inline=False
            )
            embed.add_field(
                name="🔒 Privacy",
                value="We only check for Lil' Gargs NFTs. Your wallet remains secure.",
                inline=False
            )
            await interaction.followup.send(embed=embed)
            return

        try:
            # Show verification in progress
            embed = discord.Embed(
                title="🔍 Verifying Wallet...",
                description=f"Checking wallet `{wallet[:8]}...{wallet[-8:]}` for Lil' Gargs NFTs",
                color=0xFFFF00
            )
            embed.set_footer(text="This may take a few seconds...")
            await interaction.followup.send(embed=embed)

            # Verify NFT holdings with enhanced security
            result = await bot.verify_nft_holder(wallet, str(interaction.user.id))

            if not result['success']:
                error_embed = discord.Embed(
                    title="❌ Verification Failed",
                    description=result['error'],
                    color=0xFF0000
                )

                # Add security note if available
                if 'security_note' in result:
                    error_embed.add_field(
                        name="🔒 Security Info",
                        value=result['security_note'],
                        inline=False
                    )

                if "Invalid Solana wallet address" in result['error']:
                    error_embed.add_field(
                        name="💡 Tips",
                        value="• Make sure you're using a Solana wallet address\n• Check for typos in your address\n• Address should be 32-44 characters long",
                        inline=False
                    )
                elif "not configured" in result['error']:
                    error_embed.add_field(
                        name="🛠️ Administrator",
                        value="The NFT verification service needs to be configured. Please contact a server administrator.",
                        inline=False
                    )
                elif "Rate limit exceeded" in result['error']:
                    error_embed.add_field(
                        name="⏱️ Rate Limiting",
                        value="This helps prevent abuse and ensures fair access for all users.",
                        inline=False
                    )

                await interaction.edit_original_response(embed=error_embed)
                return
            
            if result['holder']:
                # User holds Lil' Gargs NFTs
                embed = discord.Embed(
                    title="✅ Verified Lil' Gargs Holder!",
                    description=f"🎉 Found **{result['nft_count']} Lil' Gargs NFT(s)** in this wallet!",
                    color=0x00FF00,
                    timestamp=datetime.utcnow()
                )

                # Show first few NFTs
                if result['nfts']:
                    nft_list = []
                    for i, nft in enumerate(result['nfts'][:5]):
                        nft_list.append(f"**{i+1}.** {nft['name']}")

                    embed.add_field(
                        name="🖼️ Your Lil' Gargs",
                        value="\n".join(nft_list),
                        inline=False
                    )

                    if len(result['nfts']) > 5:
                        embed.add_field(
                            name="📊 Collection Stats",
                            value=f"Showing 5 of {result['nft_count']} NFTs",
                            inline=False
                        )

                # Add enhanced wallet summary if available
                if 'wallet_summary' in result:
                    embed.add_field(
                        name="📊 Wallet Summary",
                        value=result['wallet_summary'],
                        inline=False
                    )

                embed.add_field(
                    name="🔗 Wallet",
                    value=f"`{wallet[:8]}...{wallet[-8:]}`",
                    inline=True
                )

                embed.add_field(
                    name="⏰ Verified",
                    value=f"<t:{int(datetime.utcnow().timestamp())}:R>",
                    inline=True
                )

                # Add security note
                if 'security_note' in result:
                    embed.add_field(
                        name="🔒 Security",
                        value=result['security_note'],
                        inline=False
                    )
                
                # Store verification in database
                await bot.db.nft_holders.update_one(
                    {"user_id": str(interaction.user.id)},
                    {
                        "$set": {
                            "wallet_address": wallet,
                            "nft_count": result['nft_count'],
                            "verified_at": datetime.utcnow(),
                            "user_name": interaction.user.display_name
                        }
                    },
                    upsert=True
                )
                
                # Auto-assign NFT roles based on holdings
                config = await bot.db.server_configs.find_one({"guild_id": str(interaction.guild.id)})
                roles_assigned = []
                roles_failed = []

                if config and 'nft_roles' in config:
                    for nft_role_config in config['nft_roles']:
                        if result['nft_count'] >= nft_role_config['nft_threshold']:
                            role = interaction.guild.get_role(int(nft_role_config['role_id']))
                            if role and role not in interaction.user.roles:
                                try:
                                    await interaction.user.add_roles(role, reason=f"NFT verification - {result['nft_count']} NFTs")
                                    roles_assigned.append(role.name)
                                except Exception as e:
                                    logger.error(f"Failed to assign role {role.name}: {e}")
                                    roles_failed.append(role.name)

                    if roles_assigned:
                        embed.add_field(
                            name="🎭 Roles Assigned",
                            value=f"✅ {', '.join(roles_assigned)}",
                            inline=False
                        )

                    if roles_failed:
                        embed.add_field(
                            name="⚠️ Role Assignment Issues",
                            value=f"Failed to assign: {', '.join(roles_failed)}\nPlease contact an administrator.",
                            inline=False
                        )
                else:
                    embed.add_field(
                        name="ℹ️ Role Setup",
                        value="No automatic roles configured. Contact an administrator to set up NFT holder roles.",
                        inline=False
                    )

                embed.set_footer(text="Verification complete! You can re-verify anytime to update your roles.")
                
            else:
                # No Lil' Gargs NFTs found
                embed = discord.Embed(
                    title="❌ No Lil' Gargs NFTs Found",
                    description=f"The wallet `{wallet[:8]}...{wallet[-8:]}` doesn't contain any Lil' Gargs NFTs.",
                    color=0xFF9900,
                    timestamp=datetime.utcnow()
                )
                embed.add_field(
                    name="🛒 Want to join the Garg Gang?",
                    value="Check out the marketplace to get your first Lil' Garg!",
                    inline=False
                )
                embed.add_field(
                    name="🔍 Double Check",
                    value="• Make sure you're using the correct wallet address\n• Ensure your NFTs are in this specific wallet\n• Try again if you just purchased an NFT",
                    inline=False
                )
                embed.add_field(
                    name="📞 Need Help?",
                    value="Contact support if you believe this is an error.",
                    inline=False
                )
                embed.set_footer(text="Verification complete")

            await interaction.edit_original_response(embed=embed)
            
        except Exception as e:
            logger.error(f"NFT verify error: {e}")
            await interaction.followup.send("❌ Error verifying wallet. Please try again later!")
    
    elif action == "flex":
        if not token_id:
            await interaction.followup.send("❌ Please provide a token ID to flex your NFT!")
            return
        
        try:
            # Get NFT metadata
            metadata = await bot.get_nft_metadata(token_id)
            
            if not metadata:
                await interaction.followup.send(f"❌ Could not find NFT with token ID: {token_id}")
                return
            
            # Create flex embed
            embed = discord.Embed(
                title=f"✨ {metadata['name']}",
                description=metadata.get('description', 'A magnificent Lil\' Garg!'),
                color=0x7289DA
            )
            
            # Set NFT image
            embed.set_image(url=metadata['image'])
            
            # Add attributes
            if metadata['attributes']:
                attrs = []
                for attr in metadata['attributes'][:6]:  # Show first 6 attributes
                    trait_type = attr.get('trait_type', 'Unknown')
                    value = attr.get('value', 'Unknown')
                    attrs.append(f"**{trait_type}:** {value}")
                
                embed.add_field(
                    name="🎨 Traits",
                    value="\n".join(attrs),
                    inline=True
                )
            
            embed.add_field(
                name="👤 Flexed by",
                value=interaction.user.mention,
                inline=True
            )
            
            embed.add_field(
                name="🔢 Token ID",
                value=f"`{token_id}`",
                inline=True
            )
            
            embed.set_footer(text="What a beautiful Garg! 🔥")
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"NFT flex error: {e}")
            await interaction.followup.send("❌ Error displaying NFT. Please try again!")
    
    elif action == "stats":
        try:
            # Get server NFT holder stats
            total_holders = await bot.db.nft_holders.count_documents({})
            
            if total_holders == 0:
                await interaction.followup.send("📊 No verified holders yet! Use `/nft verify` to get started.")
                return
            
            # Get top holders
            top_holders = await bot.db.nft_holders.find().sort("nft_count", -1).limit(5).to_list(length=5)
            
            embed = discord.Embed(
                title="📊 Lil' Gargs Holder Stats",
                color=0x0099FF
            )
            
            embed.add_field(
                name="👥 Total Verified Holders",
                value=f"**{total_holders}** members",
                inline=True
            )
            
            # Calculate total NFTs held by verified members
            pipeline = [
                {"$group": {"_id": None, "total_nfts": {"$sum": "$nft_count"}}}
            ]
            total_nfts_result = await bot.db.nft_holders.aggregate(pipeline).to_list(length=1)
            total_nfts = total_nfts_result[0]['total_nfts'] if total_nfts_result else 0
            
            embed.add_field(
                name="🖼️ Total NFTs Held",
                value=f"**{total_nfts}** Lil' Gargs",
                inline=True
            )
            
            # Top holders leaderboard
            if top_holders:
                leaderboard = []
                for i, holder in enumerate(top_holders):
                    try:
                        user = bot.get_user(int(holder['user_id']))
                        username = user.display_name if user else holder.get('user_name', 'Unknown')
                        leaderboard.append(f"**{i+1}.** {username} - {holder['nft_count']} NFTs")
                    except:
                        leaderboard.append(f"**{i+1}.** Unknown User - {holder['nft_count']} NFTs")
                
                embed.add_field(
                    name="🏆 Top Holders",
                    value="\n".join(leaderboard),
                    inline=False
                )
            
            embed.set_footer(text="Use /nft verify to join the verified holders!")
            
            await interaction.followup.send(embed=embed)
            
        except Exception as e:
            logger.error(f"NFT stats error: {e}")
            await interaction.followup.send("❌ Error retrieving stats. Please try again!")

@bot.tree.command(name="askgarg", description="Ask the Lil' Gargs AI anything!")
@app_commands.describe(question="What would you like to know?")
async def askgarg(interaction: discord.Interaction, question: str):
    """AI question answering command"""
    await interaction.response.defer()
    
    try:
        response = await bot.get_ai_response(
            str(interaction.user.id),
            question,
            context="askgarg",
            guild_id=str(interaction.guild.id) if interaction.guild else None
        )
        
        # Split response if too long
        if len(response) > 2000:
            chunks = [response[i:i+2000] for i in range(0, len(response), 2000)]
            await interaction.followup.send(chunks[0])
            for chunk in chunks[1:]:
                await interaction.followup.send(chunk)
        else:
            await interaction.followup.send(response)
            
    except Exception as e:
        logger.error(f"askgarg error: {e}")
        await interaction.followup.send("Sorry, I encountered an error. Please try again!")

@bot.tree.command(name="suggest", description="Get a random discussion prompt from the AI!")
async def suggest(interaction: discord.Interaction):
    """AI suggestion command"""
    await interaction.response.defer()
    
    try:
        suggestion_prompt = "Give me a fun, engaging discussion topic or question for the Lil' Gargs community. Make it creative and relevant to NFTs, gaming, or community building."
        
        response = await bot.get_ai_response(
            str(interaction.user.id),
            suggestion_prompt,
            context="suggest",
            guild_id=str(interaction.guild.id) if interaction.guild else None
        )
        
        embed = discord.Embed(
            title="💡 Discussion Suggestion",
            description=response,
            color=0x7289DA
        )
        embed.set_footer(text="Use /askgarg to ask follow-up questions!")
        
        await interaction.followup.send(embed=embed)
        
    except Exception as e:
        logger.error(f"suggest error: {e}")
        await interaction.followup.send("Sorry, I couldn't generate a suggestion right now!")

@bot.tree.command(name="gargoracle", description="Consult the mystical Garg Oracle!")
@app_commands.describe(question="Ask the oracle a question...")
async def gargoracle(interaction: discord.Interaction, question: str):
    """Fun spiritual/lore-based AI mode"""
    await interaction.response.defer()
    
    try:
        oracle_prompt = f"""You are the mystical Garg Oracle, an ancient spirit of the Lil' Gargs. 
        Speak in a mystical, wise, and slightly playful tone. Give cryptic but helpful advice.
        Use emojis and mystical language. The question is: {question}"""
        
        response = await bot.get_ai_response(
            str(interaction.user.id),
            oracle_prompt,
            context="oracle",
            guild_id=str(interaction.guild.id) if interaction.guild else None
        )
        
        embed = discord.Embed(
            title="🔮 The Garg Oracle Speaks",
            description=f"*The ancient gargoyle spirits whisper...*\n\n{response}",
            color=0x9932CC
        )
        embed.set_footer(text="The oracle has spoken... ✨")
        
        await interaction.followup.send(embed=embed)
        
    except Exception as e:
        logger.error(f"gargoracle error: {e}")
        await interaction.followup.send("*The oracle's vision is clouded... try again later* 🔮")

# ========================
# ADMIN COMMANDS - DEBUG & TESTING
# ========================

@bot.tree.command(name="nftdebug", description="Debug NFT verification (Admin only)")
@app_commands.describe(wallet="Wallet address to debug")
async def nft_debug(interaction: discord.Interaction, wallet: str):
    """Debug NFT verification issues"""
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("❌ You need administrator permissions to use this command!", ephemeral=True)
        return
    
    await interaction.response.defer()
    
    try:
        # Test the Helius API connection with detailed logging
        url = f"https://mainnet.helius-rpc.com/?api-key={bot.helius_api_key}"
        
        payload = {
            "jsonrpc": "2.0",
            "id": "debug-test",
            "method": "getAssetsByOwner",
            "params": {
                "ownerAddress": wallet,
                "page": 1,
                "limit": 10  # Small limit for debugging
            }
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=payload) as response:
                status = response.status
                response_text = await response.text()
                
                embed = discord.Embed(
                    title="🔍 NFT Debug Results",
                    color=0x0099FF
                )
                
                embed.add_field(
                    name="API Status",
                    value=f"HTTP {status}",
                    inline=True
                )
                
                embed.add_field(
                    name="Wallet",
                    value=f"`{wallet[:8]}...{wallet[-8:]}`",
                    inline=True
                )
                
                embed.add_field(
                    name="Verified Creator",
                    value=f"`{bot.verified_creator[:8]}...{bot.verified_creator[-8:]}`",
                    inline=False
                )
                
                if status == 200:
                    try:
                        data = json.loads(response_text)
                        if 'result' in data:
                            assets = data['result'].get('items', [])
                            embed.add_field(
                                name="Total Assets Found",
                                value=str(len(assets)),
                                inline=True
                            )
                            
                            # Check for Lil' Gargs specifically
                            lil_gargs_count = 0
                            for asset in assets:
                                creators = asset.get('creators', [])
                                for creator in creators:
                                    if creator.get('address') == bot.verified_creator:
                                        lil_gargs_count += 1
                                        break
                            
                            embed.add_field(
                                name="Lil' Gargs Found",
                                value=str(lil_gargs_count),
                                inline=True
                            )
                            
                            # Show first few assets for inspection
                            if assets:
                                asset_info = []
                                for i, asset in enumerate(assets[:3]):
                                    creators = asset.get('creators', [])
                                    creator_addrs = [c.get('address', 'Unknown')[:8] + '...' for c in creators]
                                    name = asset.get('content', {}).get('metadata', {}).get('name', 'Unknown')
                                    asset_info.append(f"**{i+1}.** {name}\n   Creators: {', '.join(creator_addrs)}")
                                
                                embed.add_field(
                                    name="Sample Assets",
                                    value="\n\n".join(asset_info),
                                    inline=False
                                )
                        else:
                            embed.add_field(
                                name="API Response",
                                value=response_text[:500] + "..." if len(response_text) > 500 else response_text,
                                inline=False
                            )
                    except Exception as parse_error:
                        embed.add_field(
                            name="Parse Error",
                            value=str(parse_error),
                            inline=False
                        )
                else:
                    embed.add_field(
                        name="Error Response",
                        value=response_text[:500] + "..." if len(response_text) > 500 else response_text,
                        inline=False
                    )
                
                await interaction.followup.send(embed=embed)
                
    except Exception as e:
        logger.error(f"Debug error: {e}")
        await interaction.followup.send(f"❌ Debug failed: {str(e)}")

# ========================
# ADMIN COMMANDS - AI MANAGEMENT
# ========================

@bot.tree.command(name="toggleai", description="Toggle AI responses on/off (Admin only)")
async def toggleai(interaction: discord.Interaction):
    """Toggle AI functionality"""
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("❌ You need administrator permissions to use this command!", ephemeral=True)
        return
    
    bot.ai_enabled = not bot.ai_enabled
    status = "enabled ✅" if bot.ai_enabled else "disabled ❌"
    
    await interaction.response.send_message(f"AI responses are now **{status}**")

@bot.tree.command(name="clearcontext", description="Clear a user's AI conversation memory (Admin only)")
@app_commands.describe(user="User whose conversation history to clear")
async def clearcontext(interaction: discord.Interaction, user: discord.Member):
    """Clear user's AI conversation context"""
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("❌ You need administrator permissions to use this command!", ephemeral=True)
        return
    
    user_id = str(user.id)
    if user_id in bot.conversation_memory:
        del bot.conversation_memory[user_id]
        await interaction.response.send_message(f"✅ Cleared conversation context for {user.mention}")
    else:
        await interaction.response.send_message(f"No conversation context found for {user.mention}")

# ========================
# KNOWLEDGE BASE MANAGEMENT
# ========================

@bot.tree.command(name="adminadd", description="Add information to AI knowledge base (Admin only)")
@app_commands.describe(
    topic="Topic/category for this information",
    content="The information to add"
)
async def adminadd(interaction: discord.Interaction, topic: str, content: str):
    """Add knowledge to AI knowledge base"""
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("❌ You need administrator permissions to use this command!", ephemeral=True)
        return
    
    try:
        knowledge_entry = {
            "id": str(uuid.uuid4()),
            "topic": topic.lower().replace(" ", "_"),
            "content": content,
            "created_at": datetime.utcnow(),
            "created_by": str(interaction.user.id)
        }
        
        await bot.db.ai_knowledge.insert_one(knowledge_entry)
        
        embed = discord.Embed(
            title="✅ Knowledge Added",
            description=f"**Topic:** {topic}\n**Content:** {content[:200]}{'...' if len(content) > 200 else ''}",
            color=0x00FF00
        )
        
        await interaction.response.send_message(embed=embed)
        
    except Exception as e:
        logger.error(f"adminadd error: {e}")
        await interaction.response.send_message("❌ Failed to add knowledge entry!")

@bot.tree.command(name="adminlist", description="List all knowledge base entries (Admin only)")
async def adminlist(interaction: discord.Interaction):
    """List all AI knowledge entries"""
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("❌ You need administrator permissions to use this command!", ephemeral=True)
        return
    
    try:
        entries = await bot.db.ai_knowledge.find().to_list(length=None)
        
        if not entries:
            await interaction.response.send_message("📋 Knowledge base is empty!")
            return
        
        embed = discord.Embed(
            title="📋 AI Knowledge Base",
            color=0x0099FF
        )
        
        for entry in entries[:10]:  # Show first 10 entries
            embed.add_field(
                name=f"🏷️ {entry['topic']}",
                value=f"{entry['content'][:100]}{'...' if len(entry['content']) > 100 else ''}\n*ID: {entry['id'][:8]}...*",
                inline=False
            )
        
        if len(entries) > 10:
            embed.set_footer(text=f"Showing 10 of {len(entries)} entries")
        
        await interaction.response.send_message(embed=embed)
        
    except Exception as e:
        logger.error(f"adminlist error: {e}")
        await interaction.response.send_message("❌ Failed to retrieve knowledge entries!")

# ========================
# ADMIN COMMANDS - DOCUMENT & NFT CONFIGURATION
# ========================

@bot.tree.command(name="config", description="Configure bot settings (Admin only)")
@app_commands.describe(
    setting="Setting to configure",
    role="Role for the setting",
    value="Value for the setting"
)
@app_commands.choices(setting=[
    app_commands.Choice(name="document-upload-role", value="doc_upload_role"),
    app_commands.Choice(name="nft-role-add", value="nft_role_add"),
    app_commands.Choice(name="nft-role-remove", value="nft_role_remove"),
    app_commands.Choice(name="view-config", value="view_config")
])
async def config_command(interaction: discord.Interaction, setting: str, role: discord.Role = None, value: int = None):
    """Configure bot settings for documents and NFT roles"""
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("❌ You need administrator permissions to use this command!", ephemeral=True)
        return

    await interaction.response.defer()

    try:
        guild_id = str(interaction.guild.id)
        config = await bot.db.server_configs.find_one({"guild_id": guild_id}) or {"guild_id": guild_id}

        if setting == "doc_upload_role":
            if not role:
                await interaction.followup.send("❌ Please specify a role for document upload permissions!")
                return

            config['document_upload_role_id'] = str(role.id)
            await bot.db.server_configs.update_one(
                {"guild_id": guild_id},
                {"$set": config},
                upsert=True
            )

            embed = discord.Embed(
                title="✅ Document Upload Role Set",
                description=f"Users with the {role.mention} role can now upload documents.",
                color=0x00FF00
            )
            await interaction.followup.send(embed=embed)

        elif setting == "nft_role_add":
            if not role or value is None:
                await interaction.followup.send("❌ Please specify both a role and NFT threshold (minimum number of NFTs required)!")
                return

            if value < 1:
                await interaction.followup.send("❌ NFT threshold must be at least 1!")
                return

            # Initialize nft_roles if it doesn't exist
            if 'nft_roles' not in config:
                config['nft_roles'] = []

            # Check if role already exists
            existing_role = next((r for r in config['nft_roles'] if r['role_id'] == str(role.id)), None)
            if existing_role:
                existing_role['nft_threshold'] = value
                action = "updated"
            else:
                config['nft_roles'].append({
                    'role_id': str(role.id),
                    'nft_threshold': value
                })
                action = "added"

            await bot.db.server_configs.update_one(
                {"guild_id": guild_id},
                {"$set": config},
                upsert=True
            )

            embed = discord.Embed(
                title=f"✅ NFT Role {action.title()}",
                description=f"Role {role.mention} will be assigned to users with **{value}+** Lil' Gargs NFTs.",
                color=0x00FF00
            )
            await interaction.followup.send(embed=embed)

        elif setting == "nft_role_remove":
            if not role:
                await interaction.followup.send("❌ Please specify a role to remove from NFT role assignments!")
                return

            if 'nft_roles' not in config:
                await interaction.followup.send("❌ No NFT roles are currently configured!")
                return

            # Remove the role
            config['nft_roles'] = [r for r in config['nft_roles'] if r['role_id'] != str(role.id)]

            await bot.db.server_configs.update_one(
                {"guild_id": guild_id},
                {"$set": config},
                upsert=True
            )

            embed = discord.Embed(
                title="✅ NFT Role Removed",
                description=f"Role {role.mention} has been removed from automatic NFT role assignments.",
                color=0xFF9900
            )
            await interaction.followup.send(embed=embed)

        elif setting == "view_config":
            embed = discord.Embed(
                title="⚙️ Server Configuration",
                description=f"Current bot settings for {interaction.guild.name}",
                color=0x0099FF
            )

            # Document upload role
            if 'document_upload_role_id' in config:
                doc_role = interaction.guild.get_role(int(config['document_upload_role_id']))
                embed.add_field(
                    name="📄 Document Upload Role",
                    value=doc_role.mention if doc_role else "Role not found",
                    inline=False
                )
            else:
                embed.add_field(
                    name="📄 Document Upload Role",
                    value="Not configured (Admins only)",
                    inline=False
                )

            # NFT roles
            if 'nft_roles' in config and config['nft_roles']:
                nft_role_text = []
                for nft_role_config in config['nft_roles']:
                    role_obj = interaction.guild.get_role(int(nft_role_config['role_id']))
                    if role_obj:
                        nft_role_text.append(f"{role_obj.mention} - {nft_role_config['nft_threshold']}+ NFTs")
                    else:
                        nft_role_text.append(f"Role not found - {nft_role_config['nft_threshold']}+ NFTs")

                embed.add_field(
                    name="🖼️ NFT Roles",
                    value="\n".join(nft_role_text) if nft_role_text else "None configured",
                    inline=False
                )
            else:
                embed.add_field(
                    name="🖼️ NFT Roles",
                    value="None configured",
                    inline=False
                )

            embed.set_footer(text="Use /config to modify these settings")
            await interaction.followup.send(embed=embed)

    except Exception as e:
        logger.error(f"Config command error: {e}")
        await interaction.followup.send("❌ Failed to update configuration!")

# ========================
# SLASH COMMANDS - DOCUMENT MANAGEMENT
# ========================

@bot.tree.command(name="docs", description="Document management for AI knowledge base")
@app_commands.describe(
    action="Choose a document action",
    file="Document file to upload (PDF, DOCX, TXT)",
    document_id="Document ID for specific actions"
)
@app_commands.choices(action=[
    app_commands.Choice(name="upload", value="upload"),
    app_commands.Choice(name="list", value="list"),
    app_commands.Choice(name="delete", value="delete"),
    app_commands.Choice(name="summary", value="summary")
])
async def docs_command(interaction: discord.Interaction, action: str, file: discord.Attachment = None, document_id: str = None):
    """Document management commands for AI knowledge base"""
    await interaction.response.defer()

    # Check if document systems are available
    if not bot.document_processor or not bot.document_qa_system:
        await interaction.followup.send("❌ Document features are not available. Please check OpenAI API configuration.")
        return

    if action == "upload":
        # Check permissions (admin or specific role)
        if not interaction.user.guild_permissions.administrator:
            # Check for document upload role
            config = await bot.db.server_configs.find_one({"guild_id": str(interaction.guild.id)})
            if config and 'document_upload_role_id' in config:
                upload_role = interaction.guild.get_role(int(config['document_upload_role_id']))
                if not upload_role or upload_role not in interaction.user.roles:
                    await interaction.followup.send("❌ You don't have permission to upload documents!")
                    return
            else:
                await interaction.followup.send("❌ Only administrators can upload documents!")
                return

        if not file:
            await interaction.followup.send("❌ Please attach a document file to upload!")
            return

        try:
            # Download file data
            file_data = await file.read()

            # Process document
            result = await bot.document_processor.process_document(
                file_data=file_data,
                filename=file.filename,
                uploaded_by=str(interaction.user.id),
                guild_id=str(interaction.guild.id)
            )

            if result['success']:
                embed = discord.Embed(
                    title="✅ Document Uploaded Successfully",
                    description=f"**File:** {result['filename']}\n**Chunks:** {result['chunk_count']}\n**Text Length:** {result['text_length']:,} characters",
                    color=0x00FF00
                )
                embed.add_field(
                    name="📄 Document ID",
                    value=f"`{result['document_id']}`",
                    inline=False
                )
                embed.add_field(
                    name="🤖 AI Integration",
                    value="This document is now available for AI-powered Q&A using `/ask` command!",
                    inline=False
                )
                embed.set_footer(text=f"Uploaded by {interaction.user.display_name}")

                await interaction.followup.send(embed=embed)

                # Log upload
                logger.info(f"Document uploaded: {file.filename} by {interaction.user.display_name} in {interaction.guild.name}")

            else:
                await interaction.followup.send(f"❌ Upload failed: {result['error']}")

        except Exception as e:
            logger.error(f"Document upload error: {e}")
            await interaction.followup.send("❌ Failed to upload document. Please try again!")

    elif action == "list":
        try:
            documents = await bot.document_processor.get_document_list(str(interaction.guild.id))

            if not documents:
                await interaction.followup.send("📚 No documents uploaded yet! Use `/docs upload` to add documents.")
                return

            embed = discord.Embed(
                title="📚 Uploaded Documents",
                description=f"Documents available for AI Q&A in {interaction.guild.name}",
                color=0x0099FF
            )

            for i, doc in enumerate(documents[:10]):  # Limit to 10 documents
                upload_date = doc['upload_date'].strftime("%Y-%m-%d %H:%M")
                uploader = bot.get_user(int(doc['uploaded_by']))
                uploader_name = uploader.display_name if uploader else "Unknown"

                embed.add_field(
                    name=f"📄 {doc['filename']}",
                    value=f"**ID:** `{doc['document_id'][:8]}...`\n**Type:** {doc['file_type'].upper()}\n**Chunks:** {doc['chunk_count']}\n**Uploaded:** {upload_date}\n**By:** {uploader_name}",
                    inline=True
                )

            if len(documents) > 10:
                embed.set_footer(text=f"Showing 10 of {len(documents)} documents")

            await interaction.followup.send(embed=embed)

        except Exception as e:
            logger.error(f"Document list error: {e}")
            await interaction.followup.send("❌ Failed to retrieve document list!")

    elif action == "delete":
        # Check permissions
        if not interaction.user.guild_permissions.administrator:
            await interaction.followup.send("❌ Only administrators can delete documents!")
            return

        if not document_id:
            await interaction.followup.send("❌ Please provide a document ID to delete!")
            return

        try:
            success = await bot.document_processor.delete_document(document_id, str(interaction.guild.id))

            if success:
                embed = discord.Embed(
                    title="✅ Document Deleted",
                    description=f"Document `{document_id}` has been removed from the knowledge base.",
                    color=0xFF0000
                )
                await interaction.followup.send(embed=embed)
            else:
                await interaction.followup.send("❌ Document not found or you don't have permission to delete it!")

        except Exception as e:
            logger.error(f"Document delete error: {e}")
            await interaction.followup.send("❌ Failed to delete document!")

    elif action == "summary":
        if not document_id:
            await interaction.followup.send("❌ Please provide a document ID to summarize!")
            return

        try:
            summary = await bot.document_qa_system.summarize_document(document_id, str(interaction.guild.id))

            if summary:
                embed = discord.Embed(
                    title="📋 Document Summary",
                    description=summary,
                    color=0x9932CC
                )
                embed.add_field(
                    name="📄 Document ID",
                    value=f"`{document_id}`",
                    inline=False
                )
                embed.set_footer(text="Generated by AI")

                await interaction.followup.send(embed=embed)
            else:
                await interaction.followup.send("❌ Document not found or failed to generate summary!")

        except Exception as e:
            logger.error(f"Document summary error: {e}")
            await interaction.followup.send("❌ Failed to generate document summary!")

# ========================
# SLASH COMMANDS - AI DOCUMENTATION MANAGEMENT
# ========================

@bot.tree.command(name="aidocs", description="Manage AI documentation for enhanced responses")
@app_commands.describe(
    action="Choose documentation action",
    content="Documentation content (for set action)",
    section="Documentation section name (for set action)"
)
@app_commands.choices(action=[
    app_commands.Choice(name="view", value="view"),
    app_commands.Choice(name="set", value="set"),
    app_commands.Choice(name="append", value="append"),
    app_commands.Choice(name="clear", value="clear")
])
async def aidocs_command(interaction: discord.Interaction, action: str, content: str = None, section: str = None):
    """Manage custom AI documentation for this server"""

    # Check permissions
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("❌ You need administrator permissions to manage AI documentation!", ephemeral=True)
        return

    await interaction.response.defer()
    guild_id = str(interaction.guild.id)

    if action == "view":
        # Show current documentation
        guild_docs = await bot.db.guild_documentation.find_one({"guild_id": guild_id})

        embed = discord.Embed(
            title="📚 Current AI Documentation",
            color=0x0099FF
        )

        if guild_docs and guild_docs.get('documentation'):
            doc_content = guild_docs['documentation']
            if len(doc_content) > 1000:
                doc_content = doc_content[:1000] + "...\n\n*[Content truncated - use `/aidocs view` to see full documentation]*"

            embed.description = f"```\n{doc_content}\n```"
            embed.add_field(
                name="📊 Stats",
                value=f"**Length:** {len(guild_docs['documentation'])} characters\n**Last Updated:** <t:{int(guild_docs.get('updated_at', datetime.utcnow()).timestamp())}:R>",
                inline=False
            )
        else:
            embed.description = "No custom documentation set. Using default documentation."
            embed.add_field(
                name="💡 Tip",
                value="Use `/aidocs set` to add custom documentation that will enhance AI responses with server-specific information.",
                inline=False
            )

        await interaction.followup.send(embed=embed)

    elif action == "set":
        if not content:
            await interaction.followup.send("❌ Please provide content for the documentation!")
            return

        # Set new documentation
        doc_data = {
            "guild_id": guild_id,
            "documentation": content,
            "updated_at": datetime.utcnow(),
            "updated_by": str(interaction.user.id),
            "section": section or "main"
        }

        await bot.db.guild_documentation.update_one(
            {"guild_id": guild_id},
            {"$set": doc_data},
            upsert=True
        )

        embed = discord.Embed(
            title="✅ Documentation Updated",
            description=f"Successfully set custom AI documentation for this server.",
            color=0x00FF00
        )
        embed.add_field(
            name="📊 Details",
            value=f"**Length:** {len(content)} characters\n**Section:** {section or 'main'}\n**Updated by:** {interaction.user.mention}",
            inline=False
        )
        embed.add_field(
            name="🤖 Effect",
            value="AI responses will now include this custom documentation for context and enhanced answers.",
            inline=False
        )

        await interaction.followup.send(embed=embed)

    elif action == "append":
        if not content:
            await interaction.followup.send("❌ Please provide content to append!")
            return

        # Append to existing documentation
        guild_docs = await bot.db.guild_documentation.find_one({"guild_id": guild_id})
        existing_content = guild_docs.get('documentation', '') if guild_docs else ''

        new_content = existing_content + "\n\n" + content if existing_content else content

        doc_data = {
            "guild_id": guild_id,
            "documentation": new_content,
            "updated_at": datetime.utcnow(),
            "updated_by": str(interaction.user.id),
            "section": section or "main"
        }

        await bot.db.guild_documentation.update_one(
            {"guild_id": guild_id},
            {"$set": doc_data},
            upsert=True
        )

        embed = discord.Embed(
            title="✅ Documentation Appended",
            description=f"Successfully appended content to AI documentation.",
            color=0x00FF00
        )
        embed.add_field(
            name="📊 Details",
            value=f"**Added:** {len(content)} characters\n**Total Length:** {len(new_content)} characters\n**Section:** {section or 'main'}",
            inline=False
        )

        await interaction.followup.send(embed=embed)

    elif action == "clear":
        # Clear documentation
        await bot.db.guild_documentation.delete_one({"guild_id": guild_id})

        embed = discord.Embed(
            title="🗑️ Documentation Cleared",
            description="Custom AI documentation has been cleared. AI will use default documentation.",
            color=0xFF9900
        )
        embed.add_field(
            name="🔄 Reset",
            value="AI responses will now use the default Lil' Gargs documentation only.",
            inline=False
        )

        await interaction.followup.send(embed=embed)

@bot.tree.command(name="aistats", description="View AI and NFT verification usage statistics")
async def aistats_command(interaction: discord.Interaction):
    """View usage statistics for AI and NFT verification features"""

    # Check permissions (admin only for detailed stats)
    is_admin = interaction.user.guild_permissions.administrator

    await interaction.response.defer()

    try:
        user_id = str(interaction.user.id)
        guild_id = str(interaction.guild.id)

        # Get user's personal stats
        user_stats = await bot.db.user_stats.find_one({"user_id": user_id})

        embed = discord.Embed(
            title="📊 Usage Statistics",
            color=0x0099FF,
            timestamp=datetime.utcnow()
        )

        if user_stats:
            # Personal stats
            ai_requests_today = user_stats.get('ai_requests_today', 0)
            nft_verifications_today = user_stats.get('nft_verification_count_today', 0)
            last_ai_request = user_stats.get('last_ai_request')
            last_nft_verification = user_stats.get('last_nft_verification')

            embed.add_field(
                name="🤖 Your AI Usage Today",
                value=f"**Requests:** {ai_requests_today}/50\n**Last Request:** {f'<t:{int(last_ai_request.timestamp())}:R>' if last_ai_request else 'Never'}",
                inline=True
            )

            embed.add_field(
                name="🔍 Your NFT Verifications Today",
                value=f"**Verifications:** {nft_verifications_today}/10\n**Last Verification:** {f'<t:{int(last_nft_verification.timestamp())}:R>' if last_nft_verification else 'Never'}",
                inline=True
            )
        else:
            embed.add_field(
                name="📈 Your Usage",
                value="No usage recorded yet.\nStart using AI commands or NFT verification!",
                inline=False
            )

        # Rate limit info
        embed.add_field(
            name="⏱️ Rate Limits",
            value="**AI Requests:** 50 per day, 3 second cooldown\n**NFT Verification:** 10 per day, 30 second cooldown",
            inline=False
        )

        # Admin stats
        if is_admin:
            # Server-wide stats
            total_ai_requests = await bot.db.user_stats.count_documents({"ai_requests_today": {"$gt": 0}})
            total_nft_verifications = await bot.db.nft_holders.count_documents({"guild_id": guild_id})

            embed.add_field(
                name="🏢 Server Stats (Admin Only)",
                value=f"**Active AI Users Today:** {total_ai_requests}\n**Total NFT Holders:** {total_nft_verifications}",
                inline=False
            )

            # Documentation status
            guild_docs = await bot.db.guild_documentation.find_one({"guild_id": guild_id})
            doc_status = "✅ Custom documentation active" if guild_docs else "📝 Using default documentation"

            embed.add_field(
                name="📚 AI Documentation",
                value=doc_status,
                inline=False
            )

        embed.set_footer(text="Statistics reset daily at midnight UTC")
        await interaction.followup.send(embed=embed)

    except Exception as e:
        logger.error(f"Error in aistats command: {e}")
        await interaction.followup.send("❌ An error occurred while fetching statistics. Please try again later.")

@bot.tree.command(name="systemhealth", description="Check system health and service status (Admin only)")
async def systemhealth_command(interaction: discord.Interaction):
    """Check system health and service status"""

    # Check permissions
    if not interaction.user.guild_permissions.administrator:
        await interaction.response.send_message("❌ You need administrator permissions to check system health!", ephemeral=True)
        return

    await interaction.response.defer()

    try:
        health_status = await bot._check_system_health()

        embed = discord.Embed(
            title="🏥 System Health Check",
            color=0x00FF00 if health_status.get("errors_last_hour", 0) < 5 else 0xFF9900,
            timestamp=health_status.get("timestamp", datetime.utcnow())
        )

        # Service status
        services = {
            "🗄️ Database": health_status.get("database", "unknown"),
            "🤖 AI Service (Gemini)": health_status.get("ai_service", "unknown"),
            "🔍 NFT Service (Helius)": health_status.get("nft_service", "unknown")
        }

        service_status = []
        for service, status in services.items():
            if status == "healthy" or status == "configured":
                service_status.append(f"{service}: ✅ {status.title()}")
            elif status == "not_configured":
                service_status.append(f"{service}: ⚠️ Not Configured")
            else:
                service_status.append(f"{service}: ❌ {status.title()}")

        embed.add_field(
            name="🔧 Service Status",
            value="\n".join(service_status),
            inline=False
        )

        # Error monitoring
        error_count = health_status.get("errors_last_hour", 0)
        error_status = "🟢 Low" if error_count < 5 else "🟡 Medium" if error_count < 20 else "🔴 High"

        embed.add_field(
            name="⚠️ Error Monitoring",
            value=f"**Errors (Last Hour):** {error_count}\n**Status:** {error_status}",
            inline=True
        )

        # Bot uptime
        uptime = datetime.utcnow() - bot.start_time if hasattr(bot, 'start_time') else timedelta(0)
        embed.add_field(
            name="⏱️ Uptime",
            value=f"{uptime.days}d {uptime.seconds//3600}h {(uptime.seconds//60)%60}m",
            inline=True
        )

        # Recommendations
        recommendations = []
        if health_status.get("database") != "healthy":
            recommendations.append("• Check database connection")
        if health_status.get("ai_service") == "not_configured":
            recommendations.append("• Configure Gemini API key")
        if health_status.get("nft_service") == "not_configured":
            recommendations.append("• Configure Helius API key")
        if error_count > 10:
            recommendations.append("• Review error logs for issues")

        if recommendations:
            embed.add_field(
                name="💡 Recommendations",
                value="\n".join(recommendations),
                inline=False
            )
        else:
            embed.add_field(
                name="✅ Status",
                value="All systems operating normally!",
                inline=False
            )

        await interaction.followup.send(embed=embed)

    except Exception as e:
        logger.error(f"Error in systemhealth command: {e}")
        await bot._log_error("systemhealth_command", str(e), str(interaction.user.id), str(interaction.guild.id))
        await interaction.followup.send("❌ An error occurred while checking system health. Please try again later.")

# ========================
# SLASH COMMANDS - DOCUMENT-BASED AI CHAT
# ========================

@bot.tree.command(name="ask", description="Ask questions about uploaded documents")
@app_commands.describe(question="Your question about the project documents")
async def ask_command(interaction: discord.Interaction, question: str):
    """Ask AI questions based on uploaded documents"""
    await interaction.response.defer()

    # Check if document systems are available
    if not bot.document_qa_system:
        await interaction.followup.send("❌ Document-based AI is not available. Please check configuration.")
        return

    try:
        # Get AI response based on documents
        result = await bot.document_qa_system.answer_question(
            question=question,
            guild_id=str(interaction.guild.id),
            user_id=str(interaction.user.id)
        )

        if result['success']:
            embed = discord.Embed(
                title="🤖 AI Response",
                description=result['answer'],
                color=0x00FF00
            )

            # Add source information
            if result['sources']:
                source_text = []
                for i, source in enumerate(result['sources'][:3]):  # Show top 3 sources
                    source_text.append(f"**{i+1}.** {source['filename']} (similarity: {source['similarity']:.2f})")

                embed.add_field(
                    name="📚 Sources",
                    value="\n".join(source_text),
                    inline=False
                )

            embed.add_field(
                name="❓ Question",
                value=question,
                inline=False
            )

            embed.set_footer(text=f"Asked by {interaction.user.display_name} • Based on {result.get('chunk_count', 0)} document chunks")

            await interaction.followup.send(embed=embed)

        else:
            # Handle different error cases
            if "No relevant documents found" in result['error']:
                embed = discord.Embed(
                    title="📚 No Documents Available",
                    description="No documents have been uploaded yet for this server. Ask an administrator to upload project documents using `/docs upload`.",
                    color=0xFF9900
                )
                embed.add_field(
                    name="💡 Tip",
                    value="Once documents are uploaded, I'll be able to answer questions about your project!",
                    inline=False
                )
            elif "No sufficiently relevant information" in result['error']:
                embed = discord.Embed(
                    title="🔍 No Relevant Information Found",
                    description="I couldn't find information relevant to your question in the uploaded documents.",
                    color=0xFF9900
                )
                embed.add_field(
                    name="💡 Try",
                    value="• Rephrasing your question\n• Being more specific\n• Asking about topics covered in the uploaded documents",
                    inline=False
                )
            else:
                embed = discord.Embed(
                    title="❌ Error",
                    description=result['error'],
                    color=0xFF0000
                )

            await interaction.followup.send(embed=embed)

    except Exception as e:
        logger.error(f"Ask command error: {e}")
        await interaction.followup.send("❌ Failed to process your question. Please try again!")

# Run the bot
if __name__ == "__main__":
    bot.run(os.getenv('DISCORD_BOT_TOKEN'))
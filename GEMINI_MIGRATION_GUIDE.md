# Gemini API Migration Guide

## 🔄 Migration from Emergent.ai to Direct Gemini API

The bot has been migrated from using `emergentintegrations` (which required Emergent.ai to be online) to using the official Google Gemini API directly. This ensures the bot works independently without relying on external services.

## 📦 Installation Steps

### 1. Update Dependencies

First, uninstall the old dependency and install the new one:

```bash
# Remove old dependency
pip uninstall emergentintegrations

# Install new dependencies
pip install google-generativeai>=0.3.0
```

Or install all dependencies from the updated requirements.txt:

```bash
pip install -r backend/requirements.txt
```

### 2. Get Google Gemini API Key

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated API key

### 3. Update Environment Variables

Update your `.env` file with the Gemini API key:

```env
# Replace your existing GEMINI_API_KEY with the new one from Google AI Studio
GEMINI_API_KEY=your_google_gemini_api_key_here
```

### 4. Test the Migration

Start your bot and check the logs for:

```
✅ Gemini API connection successful
```

If you see this message, the migration was successful!

## 🔧 What Changed

### Code Changes Made

1. **Removed Emergent.ai dependency**:
   - Replaced `from emergentintegrations.llm.chat import LlmChat, UserMessage`
   - With `import google.generativeai as genai`

2. **Updated AI response function**:
   - Now uses direct Google Gemini API calls
   - Maintains conversation history locally
   - Better error handling for API-specific issues

3. **Enhanced startup testing**:
   - Tests Gemini API connection on bot startup
   - Disables AI features if API is unavailable
   - Provides clear error messages

4. **Improved system health monitoring**:
   - Actually tests Gemini API connectivity
   - Reports real-time API status

### Benefits of Migration

✅ **Independence**: No longer depends on Emergent.ai service availability
✅ **Reliability**: Direct API connection is more stable
✅ **Performance**: Faster response times without middleware
✅ **Control**: Full control over API configuration and error handling
✅ **Cost**: Direct billing with Google (potentially lower costs)

## 🚨 Troubleshooting

### Common Issues

#### 1. "Gemini API connection failed"
- **Cause**: Invalid or missing API key
- **Solution**: 
  1. Verify your API key is correct in `.env`
  2. Check that the API key has proper permissions
  3. Ensure you have Gemini API access enabled

#### 2. "API_KEY" error in logs
- **Cause**: API key not configured or invalid
- **Solution**: 
  1. Get a new API key from Google AI Studio
  2. Update your `.env` file
  3. Restart the bot

#### 3. "QUOTA" error in logs
- **Cause**: API quota exceeded
- **Solution**:
  1. Check your Google Cloud Console for quota limits
  2. Wait for quota reset or upgrade your plan
  3. Monitor usage with `/systemhealth` command

#### 4. AI responses not working
- **Cause**: Various API issues
- **Solution**:
  1. Use `/systemhealth` to check AI service status
  2. Check bot logs for specific error messages
  3. Verify internet connectivity

### Testing Commands

After migration, test these commands:

```bash
# Test AI functionality
/askgarg What are Lil' Gargs?

# Check system health
/systemhealth

# View AI usage stats
/aistats
```

## 📊 API Usage and Costs

### Google Gemini API Pricing (as of 2024)

- **Gemini 1.5 Flash**: 
  - Input: $0.075 per 1M tokens
  - Output: $0.30 per 1M tokens
- **Free tier**: 15 requests per minute, 1,500 requests per day

### Usage Optimization

The bot includes several optimizations:
- **Rate limiting**: 50 requests per user per day
- **Token limits**: Max 1000 tokens per response
- **Conversation memory**: Only keeps last 3 exchanges for context
- **Error handling**: Graceful degradation when quota exceeded

## 🔄 Rollback Plan

If you need to rollback to the old system:

1. **Reinstall old dependency**:
   ```bash
   pip install emergentintegrations
   ```

2. **Revert code changes**:
   ```bash
   git checkout HEAD~1 backend/discord_bot.py
   ```

3. **Update environment**:
   - Ensure Emergent.ai service is available
   - Use your old Emergent.ai API configuration

## 📞 Support

If you encounter issues during migration:

1. **Check logs**: Look for specific error messages in bot logs
2. **Test API**: Use `/systemhealth` to check service status
3. **Verify setup**: Ensure all environment variables are correct
4. **Check documentation**: Review Google Gemini API documentation

## ✅ Migration Checklist

- [ ] Uninstalled `emergentintegrations`
- [ ] Installed `google-generativeai`
- [ ] Obtained Google Gemini API key
- [ ] Updated `.env` file with new API key
- [ ] Started bot and verified "✅ Gemini API connection successful" message
- [ ] Tested AI commands (`/askgarg`, `/suggest`, etc.)
- [ ] Verified system health with `/systemhealth`
- [ ] Confirmed AI features work without Emergent.ai dependency

## 🎉 Success!

Once you see "✅ Gemini API connection successful" in your logs and AI commands work properly, your migration is complete! Your bot now runs independently with direct Google Gemini API integration.

The bot will now:
- Work even when Emergent.ai is offline
- Have faster response times
- Provide better error handling
- Give you full control over AI functionality

Enjoy your newly independent Lil' Gargs bot! 🤖✨

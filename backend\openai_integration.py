"""
OpenAI Integration Module for Document-Based Q&A
Handles ChatGPT API integration with document retrieval for grounded responses
"""

import logging
from typing import Dict, List, Any, Optional
import openai
from datetime import datetime

logger = logging.getLogger(__name__)

class DocumentQASystem:
    """Handles document-based Q&A using OpenAI ChatGPT"""
    
    def __init__(self, openai_api_key: str, document_processor):
        self.openai_client = openai.AsyncOpenAI(api_key=openai_api_key)
        self.document_processor = document_processor
        
        # Configuration
        self.model = "gpt-4o-mini"  # Cost-effective model for Q&A
        self.max_tokens = 1000
        self.temperature = 0.1  # Low temperature for factual responses
        self.max_context_chunks = 5  # Maximum chunks to include in context
    
    async def answer_question(self, question: str, guild_id: str, user_id: str) -> Dict[str, Any]:
        """Answer a question based on uploaded documents"""
        try:
            # Search for relevant document chunks
            relevant_chunks = await self.document_processor.search_documents(
                query=question,
                guild_id=guild_id,
                top_k=self.max_context_chunks
            )
            
            if not relevant_chunks:
                return {
                    'success': False,
                    'error': 'No relevant documents found. Please upload documents first using `/docs upload`.',
                    'answer': None,
                    'sources': []
                }
            
            # Filter chunks with reasonable similarity threshold
            filtered_chunks = [chunk for chunk in relevant_chunks if chunk['similarity'] > 0.3]
            
            if not filtered_chunks:
                return {
                    'success': False,
                    'error': 'No sufficiently relevant information found in uploaded documents.',
                    'answer': None,
                    'sources': []
                }
            
            # Prepare context from relevant chunks
            context_text = self._prepare_context(filtered_chunks)
            
            # Generate response using OpenAI
            response = await self._generate_response(question, context_text)
            
            if not response:
                return {
                    'success': False,
                    'error': 'Failed to generate response from OpenAI.',
                    'answer': None,
                    'sources': []
                }
            
            # Prepare source information
            sources = self._prepare_sources(filtered_chunks)
            
            # Log the interaction
            await self._log_interaction(user_id, guild_id, question, response, sources)
            
            return {
                'success': True,
                'answer': response,
                'sources': sources,
                'chunk_count': len(filtered_chunks)
            }
            
        except Exception as e:
            logger.error(f"Error answering question: {e}")
            return {
                'success': False,
                'error': f'An error occurred while processing your question: {str(e)}',
                'answer': None,
                'sources': []
            }
    
    def _prepare_context(self, chunks: List[Dict[str, Any]]) -> str:
        """Prepare context text from relevant chunks"""
        context_parts = []
        
        for i, chunk in enumerate(chunks):
            context_parts.append(
                f"[Source {i+1}: {chunk['filename']}]\n{chunk['text']}\n"
            )
        
        return "\n---\n".join(context_parts)
    
    async def _generate_response(self, question: str, context: str) -> Optional[str]:
        """Generate response using OpenAI ChatGPT"""
        try:
            system_prompt = """You are a helpful AI assistant for the Lil' Gargs community. You answer questions based ONLY on the provided document context about the Lil' Gargs project.

IMPORTANT GUIDELINES:
1. Only use information from the provided context documents
2. If the context doesn't contain enough information to answer the question, say so clearly
3. Always cite which source document your information comes from
4. Be accurate and factual - don't make up information
5. Keep responses concise but comprehensive
6. If asked about something not in the documents, explain that you can only answer based on uploaded documents

Format your response clearly and reference the source documents when appropriate."""

            user_prompt = f"""Based on the following context documents about Lil' Gargs, please answer this question:

QUESTION: {question}

CONTEXT DOCUMENTS:
{context}

Please provide a helpful answer based only on the information in these documents."""

            response = await self.openai_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"Error generating OpenAI response: {e}")
            return None
    
    def _prepare_sources(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Prepare source information for response"""
        sources = []
        seen_documents = set()
        
        for chunk in chunks:
            doc_id = chunk['document_id']
            if doc_id not in seen_documents:
                sources.append({
                    'filename': chunk['filename'],
                    'document_id': doc_id,
                    'similarity': round(chunk['similarity'], 3),
                    'chunk_index': chunk['chunk_index']
                })
                seen_documents.add(doc_id)
        
        return sources
    
    async def _log_interaction(self, user_id: str, guild_id: str, question: str, answer: str, sources: List[Dict[str, Any]]):
        """Log Q&A interaction for analytics"""
        try:
            interaction_record = {
                "interaction_id": str(__import__('uuid').uuid4()),
                "user_id": user_id,
                "guild_id": guild_id,
                "question": question,
                "answer": answer,
                "sources_used": sources,
                "timestamp": datetime.utcnow(),
                "model_used": self.model
            }
            
            await self.document_processor.db.qa_interactions.insert_one(interaction_record)
            
        except Exception as e:
            logger.error(f"Error logging interaction: {e}")
    
    async def get_qa_history(self, user_id: str, guild_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent Q&A history for a user"""
        try:
            interactions = await self.document_processor.db.qa_interactions.find(
                {"user_id": user_id, "guild_id": guild_id},
                {"question": 1, "answer": 1, "timestamp": 1, "sources_used": 1}
            ).sort("timestamp", -1).limit(limit).to_list(length=None)
            
            return interactions
            
        except Exception as e:
            logger.error(f"Error getting Q&A history: {e}")
            return []
    
    async def get_popular_questions(self, guild_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get most frequently asked questions in a guild"""
        try:
            pipeline = [
                {"$match": {"guild_id": guild_id}},
                {"$group": {
                    "_id": "$question",
                    "count": {"$sum": 1},
                    "last_asked": {"$max": "$timestamp"}
                }},
                {"$sort": {"count": -1}},
                {"$limit": limit}
            ]
            
            popular_questions = await self.document_processor.db.qa_interactions.aggregate(pipeline).to_list(length=None)
            
            return [
                {
                    "question": item["_id"],
                    "count": item["count"],
                    "last_asked": item["last_asked"]
                }
                for item in popular_questions
            ]
            
        except Exception as e:
            logger.error(f"Error getting popular questions: {e}")
            return []
    
    async def summarize_document(self, document_id: str, guild_id: str) -> Optional[str]:
        """Generate a summary of a specific document"""
        try:
            # Get document chunks
            chunks = await self.document_processor.db.document_chunks.find(
                {"document_id": document_id}
            ).sort("chunk_index", 1).to_list(length=None)
            
            if not chunks:
                return None
            
            # Get document info
            document = await self.document_processor.db.documents.find_one({
                "document_id": document_id,
                "guild_id": guild_id
            })
            
            if not document:
                return None
            
            # Combine chunks for summarization (limit to avoid token limits)
            text_parts = [chunk['text'] for chunk in chunks[:10]]  # First 10 chunks
            combined_text = "\n\n".join(text_parts)
            
            # Generate summary
            system_prompt = """You are a helpful AI assistant. Create a concise but comprehensive summary of the provided document about Lil' Gargs. Focus on the key points, important details, and main topics covered."""
            
            user_prompt = f"""Please create a summary of this document titled "{document['filename']}":

{combined_text}

Provide a clear, well-structured summary that captures the main points and important information."""
            
            response = await self.openai_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=500,
                temperature=0.3
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"Error summarizing document: {e}")
            return None

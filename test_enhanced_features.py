#!/usr/bin/env python3
"""
Comprehensive test suite for enhanced Lil' Gargs Discord bot features.
Tests NFT verification, AI integration, rate limiting, and error handling.
"""

import asyncio
import pytest
import os
import sys
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
import aiohttp

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from discord_bot import LilGargsBot
from motor.motor_asyncio import AsyncIOMotorClient

class TestEnhancedNFTVerification:
    """Test suite for enhanced NFT verification system"""
    
    @pytest.fixture
    async def bot(self):
        """Create a test bot instance"""
        bot = LilGargsBot()
        # Mock database connection
        bot.db = AsyncMock()
        bot.helius_api_key = "test_key"
        bot.verified_creator = "test_creator"
        return bot
    
    @pytest.mark.asyncio
    async def test_valid_wallet_address_validation(self, bot):
        """Test wallet address validation with valid addresses"""
        valid_addresses = [
            "DYw8jCTfwHNRJhhmFcbXvVDTqWMEVFBX6ZKUmG5CNSKK",
            "********************************",
            "So111111111********************************"
        ]
        
        for address in valid_addresses:
            assert bot._is_valid_solana_address(address), f"Address {address} should be valid"
    
    @pytest.mark.asyncio
    async def test_invalid_wallet_address_validation(self, bot):
        """Test wallet address validation with invalid addresses"""
        invalid_addresses = [
            "",
            "short",
            "this_is_way_too_long_to_be_a_valid_solana_address_and_should_fail",
            "invalid_characters_!@#$%",
            None
        ]
        
        for address in invalid_addresses:
            assert not bot._is_valid_solana_address(address), f"Address {address} should be invalid"
    
    @pytest.mark.asyncio
    async def test_rate_limiting_verification(self, bot):
        """Test NFT verification rate limiting"""
        user_id = "test_user_123"
        
        # Mock user stats with recent verification
        bot.db.user_stats.find_one.return_value = {
            "user_id": user_id,
            "last_nft_verification": datetime.utcnow() - timedelta(seconds=10),
            "nft_verification_count_today": 5,
            "nft_verification_reset_date": datetime.utcnow()
        }
        
        rate_limit_result = await bot._check_verification_rate_limit(user_id)
        
        # Should be rate limited due to 30-second cooldown
        assert not rate_limit_result["allowed"]
        assert rate_limit_result["wait_time"] > 0
    
    @pytest.mark.asyncio
    async def test_daily_limit_verification(self, bot):
        """Test daily verification limit"""
        user_id = "test_user_456"
        
        # Mock user stats with daily limit reached
        bot.db.user_stats.find_one.return_value = {
            "user_id": user_id,
            "last_nft_verification": datetime.utcnow() - timedelta(hours=1),
            "nft_verification_count_today": 10,  # Daily limit reached
            "nft_verification_reset_date": datetime.utcnow()
        }
        
        rate_limit_result = await bot._check_verification_rate_limit(user_id)
        
        # Should be rate limited due to daily limit
        assert not rate_limit_result["allowed"]
        assert "Daily verification limit" in rate_limit_result["reason"]
    
    @pytest.mark.asyncio
    async def test_successful_nft_verification(self, bot):
        """Test successful NFT verification with mocked API response"""
        wallet_address = "DYw8jCTfwHNRJhhmFcbXvVDTqWMEVFBX6ZKUmG5CNSKK"
        user_id = "test_user_789"
        
        # Mock rate limiting to allow verification
        bot.db.user_stats.find_one.return_value = None
        bot.db.user_stats.update_one = AsyncMock()
        
        # Mock successful API response
        mock_response_data = {
            "result": {
                "items": [
                    {
                        "id": "test_nft_1",
                        "creators": [
                            {
                                "address": "test_creator",
                                "verified": True
                            }
                        ],
                        "content": {
                            "metadata": {
                                "name": "Test Lil Garg #1",
                                "attributes": []
                            },
                            "files": [{"uri": "test_image.png"}]
                        },
                        "grouping": [{"group_value": "test_collection"}]
                    }
                ]
            }
        }
        
        with patch('aiohttp.ClientSession') as mock_session:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = mock_response_data
            mock_session.return_value.__aenter__.return_value.post.return_value.__aenter__.return_value = mock_response
            
            result = await bot.verify_nft_holder(wallet_address, user_id)
            
            assert result["success"]
            assert result["holder"]
            assert result["nft_count"] == 1
            assert len(result["nfts"]) == 1
            assert "security_note" in result

class TestEnhancedAIIntegration:
    """Test suite for enhanced AI integration"""
    
    @pytest.fixture
    async def bot(self):
        """Create a test bot instance"""
        bot = LilGargsBot()
        bot.db = AsyncMock()
        bot.gemini_api_key = "test_gemini_key"
        bot.ai_enabled = True
        return bot
    
    @pytest.mark.asyncio
    async def test_ai_rate_limiting(self, bot):
        """Test AI request rate limiting"""
        user_id = "test_user_ai"
        
        # Mock user stats with recent AI request
        bot.db.user_stats.find_one.return_value = {
            "user_id": user_id,
            "last_ai_request": datetime.utcnow() - timedelta(seconds=1),
            "ai_requests_today": 25,
            "ai_reset_date": datetime.utcnow()
        }
        
        rate_limit_result = await bot._check_ai_rate_limit(user_id)
        
        # Should be rate limited due to 3-second cooldown
        assert not rate_limit_result["allowed"]
        assert rate_limit_result["wait_time"] > 0
    
    @pytest.mark.asyncio
    async def test_ai_daily_limit(self, bot):
        """Test AI daily request limit"""
        user_id = "test_user_ai_limit"
        
        # Mock user stats with daily limit reached
        bot.db.user_stats.find_one.return_value = {
            "user_id": user_id,
            "last_ai_request": datetime.utcnow() - timedelta(hours=1),
            "ai_requests_today": 50,  # Daily limit reached
            "ai_reset_date": datetime.utcnow()
        }
        
        rate_limit_result = await bot._check_ai_rate_limit(user_id)
        
        # Should be rate limited due to daily limit
        assert not rate_limit_result["allowed"]
        assert "Daily AI request limit" in rate_limit_result["reason"]
    
    @pytest.mark.asyncio
    async def test_guild_documentation_loading(self, bot):
        """Test loading guild-specific documentation"""
        guild_id = "test_guild_123"
        
        # Mock guild documentation
        bot.db.guild_documentation.find_one.return_value = {
            "guild_id": guild_id,
            "documentation": "Custom server documentation for testing"
        }
        
        documentation = await bot._load_guild_documentation(guild_id)
        
        assert "Custom server documentation for testing" in documentation
    
    @pytest.mark.asyncio
    async def test_default_documentation_fallback(self, bot):
        """Test fallback to default documentation"""
        guild_id = "test_guild_no_docs"
        
        # Mock no guild documentation found
        bot.db.guild_documentation.find_one.return_value = None
        
        documentation = await bot._load_guild_documentation(guild_id)
        
        # Should return default documentation
        assert "Lil' Gargs Bot Documentation" in documentation
        assert "NFT Verification" in documentation

class TestErrorHandlingAndMonitoring:
    """Test suite for error handling and monitoring features"""
    
    @pytest.fixture
    async def bot(self):
        """Create a test bot instance"""
        bot = LilGargsBot()
        bot.db = AsyncMock()
        return bot
    
    @pytest.mark.asyncio
    async def test_error_logging(self, bot):
        """Test error logging functionality"""
        error_type = "test_error"
        error_message = "This is a test error"
        user_id = "test_user"
        guild_id = "test_guild"
        
        bot.db.error_logs.insert_one = AsyncMock()
        
        await bot._log_error(error_type, error_message, user_id, guild_id)
        
        # Verify error was logged
        bot.db.error_logs.insert_one.assert_called_once()
        call_args = bot.db.error_logs.insert_one.call_args[0][0]
        
        assert call_args["error_type"] == error_type
        assert call_args["error_message"] == error_message
        assert call_args["user_id"] == user_id
        assert call_args["guild_id"] == guild_id
    
    @pytest.mark.asyncio
    async def test_system_health_check(self, bot):
        """Test system health monitoring"""
        # Mock database ping
        bot.db.admin.command = AsyncMock(return_value={"ok": 1})
        bot.db.error_logs.count_documents = AsyncMock(return_value=3)
        
        # Set API keys
        bot.gemini_api_key = "test_key"
        bot.helius_api_key = "test_key"
        
        health_status = await bot._check_system_health()
        
        assert health_status["database"] == "healthy"
        assert health_status["ai_service"] == "configured"
        assert health_status["nft_service"] == "configured"
        assert health_status["errors_last_hour"] == 3

class TestIntegrationScenarios:
    """Integration tests for complete user scenarios"""
    
    @pytest.fixture
    async def bot(self):
        """Create a test bot instance"""
        bot = LilGargsBot()
        bot.db = AsyncMock()
        bot.helius_api_key = "test_key"
        bot.gemini_api_key = "test_key"
        bot.verified_creator = "test_creator"
        bot.ai_enabled = True
        return bot
    
    @pytest.mark.asyncio
    async def test_new_user_verification_flow(self, bot):
        """Test complete verification flow for new user"""
        user_id = "new_user_123"
        wallet_address = "DYw8jCTfwHNRJhhmFcbXvVDTqWMEVFBX6ZKUmG5CNSKK"
        
        # Mock new user (no existing stats)
        bot.db.user_stats.find_one.return_value = None
        bot.db.user_stats.update_one = AsyncMock()
        bot.db.nft_holders.update_one = AsyncMock()
        
        # Mock successful API response
        mock_response_data = {
            "result": {
                "items": [
                    {
                        "id": "test_nft_1",
                        "creators": [{"address": "test_creator", "verified": True}],
                        "content": {
                            "metadata": {"name": "Test Lil Garg #1", "attributes": []},
                            "files": [{"uri": "test_image.png"}]
                        },
                        "grouping": [{"group_value": "test_collection"}]
                    }
                ]
            }
        }
        
        with patch('aiohttp.ClientSession') as mock_session:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = mock_response_data
            mock_session.return_value.__aenter__.return_value.post.return_value.__aenter__.return_value = mock_response
            
            result = await bot.verify_nft_holder(wallet_address, user_id)
            
            # Verify successful verification
            assert result["success"]
            assert result["holder"]
            assert result["nft_count"] == 1
            
            # Verify rate limiting was updated
            bot.db.user_stats.update_one.assert_called()
            
            # Verify verification result was stored
            bot.db.nft_holders.update_one.assert_called()

# Test runner
if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v", "--tb=short"])

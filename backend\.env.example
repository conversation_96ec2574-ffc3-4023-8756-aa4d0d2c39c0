# ================================
# LIL GARGS DISCORD BOT CONFIGURATION
# ================================

# Discord Bot Configuration
DISCORD_BOT_TOKEN=your_discord_bot_token_here

# Database Configuration
MONGO_URL=mongodb://localhost:27017
DB_NAME=lil_gargs_bot

# AI Integration APIs
# OpenAI API for document-based Q&A (GPT-4o-mini)
OPENAI_API_KEY=your_openai_api_key_here

# Gemini API for general chat responses
GEMINI_API_KEY=your_gemini_api_key_here

# Blockchain/NFT Configuration
# Helius API for Solana NFT verification
HELIUS_API_KEY=your_helius_api_key_here

# Lil Gargs NFT Collection Details
VERIFIED_CREATOR=9fT6Spqbv9FxK7Ktxr6bDfASWc6k5acUNr1zMv5WrGfA
NFT_CONTRACT_ADDRESS=FP2bGBGHWrW4w82hsSDGc5zNLQ83CvEmW2shGkttS7aZ

# IPFS Configuration (Optional - for NFT metadata)
IPFS_IMAGE_FOLDER=https://your-ipfs-gateway.com/images
IPFS_JSON_FOLDER=https://your-ipfs-gateway.com/metadata

# ================================
# SETUP INSTRUCTIONS
# ================================

# 1. DISCORD BOT SETUP:
#    - Go to https://discord.com/developers/applications
#    - Create a new application and bot
#    - Copy the bot token to DISCORD_BOT_TOKEN
#    - Enable necessary intents (Message Content, Server Members, Guilds)

# 2. MONGODB SETUP:
#    - Install MongoDB locally or use MongoDB Atlas
#    - Update MONGO_URL with your connection string
#    - The bot will automatically create required collections

# 3. OPENAI API SETUP:
#    - Go to https://platform.openai.com/api-keys
#    - Create a new API key
#    - Add it to OPENAI_API_KEY
#    - This enables document-based Q&A functionality

# 4. GEMINI API SETUP:
#    - Go to https://makersuite.google.com/app/apikey
#    - Create a new API key
#    - Add it to GEMINI_API_KEY
#    - This enables general AI chat responses

# 5. HELIUS API SETUP:
#    - Go to https://helius.xyz/
#    - Create an account and get an API key
#    - Add it to HELIUS_API_KEY
#    - This enables Solana NFT verification

# 6. NFT COLLECTION SETUP:
#    - VERIFIED_CREATOR: The verified creator address for your NFT collection
#    - NFT_CONTRACT_ADDRESS: Your NFT collection's contract address
#    - These are pre-configured for Lil Gargs collection

# ================================
# FEATURE CONFIGURATION
# ================================

# Required for basic bot functionality:
# - DISCORD_BOT_TOKEN (required)
# - MONGO_URL (required)
# - DB_NAME (required)

# Required for NFT verification:
# - HELIUS_API_KEY (required)
# - VERIFIED_CREATOR (required)
# - NFT_CONTRACT_ADDRESS (required)

# Required for document-based AI chat:
# - OPENAI_API_KEY (required)

# Required for general AI chat:
# - GEMINI_API_KEY (required)

# Optional features:
# - IPFS_IMAGE_FOLDER (for NFT image display)
# - IPFS_JSON_FOLDER (for NFT metadata)

# ================================
# COST CONSIDERATIONS
# ================================

# OpenAI API Costs (Document Q&A):
# - GPT-4o-mini: ~$0.15 per 1M input tokens, ~$0.60 per 1M output tokens
# - Embeddings: ~$0.02 per 1M tokens
# - Estimated cost: $1-5 per month for small-medium communities

# Gemini API Costs (General Chat):
# - Gemini Flash: Free tier available (15 requests/minute)
# - Paid tier: Very cost-effective for chat responses

# Helius API Costs (NFT Verification):
# - Free tier: 100 requests/day
# - Paid tiers start at $9/month for higher limits

# MongoDB Costs:
# - MongoDB Atlas: Free tier (512MB) sufficient for small communities
# - Local MongoDB: Free

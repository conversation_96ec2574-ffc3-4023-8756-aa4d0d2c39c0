#!/usr/bin/env python3
import unittest
import asyncio
import os
import sys
import logging
from motor.motor_asyncio import AsyncIOMotorClient
from dotenv import load_dotenv
import requests
import json
import time
from pymongo import MongoClient
import importlib.util

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv('/app/backend/.env')

class DiscordBotTest(unittest.TestCase):
    """Test cases for the Discord bot backend functionality"""
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment"""
        # Connect to MongoDB
        cls.mongo_url = os.getenv('MONGO_URL')
        cls.db_name = os.getenv('DB_NAME')
        cls.mongo_client = MongoClient(cls.mongo_url)
        cls.db = cls.mongo_client[cls.db_name]
        
        # Discord bot token and Gemini API key
        cls.discord_token = os.getenv('DISCORD_BOT_TOKEN')
        cls.gemini_api_key = os.getenv('GEMINI_API_KEY')
        
        # Check if bot is running
        cls.bot_running = False
        try:
            result = os.popen("ps aux | grep discord_bot.py | grep -v grep").read()
            if result:
                cls.bot_running = True
                logger.info("Discord bot is running")
            else:
                logger.warning("Discord bot is not running")
        except Exception as e:
            logger.error(f"Error checking bot status: {e}")
    
    @classmethod
    def tearDownClass(cls):
        """Clean up after tests"""
        cls.mongo_client.close()
    
    def test_01_bot_connection(self):
        """Test if the bot is connected to Discord"""
        self.assertTrue(self.bot_running, "Discord bot should be running")
        
        # Check bot log for successful connection
        try:
            with open('/app/backend/bot.log', 'r') as f:
                log_content = f.read()
                self.assertIn("has connected to Discord", log_content, 
                             "Bot should be connected to Discord according to logs")
                self.assertIn("Bot is in", log_content, 
                             "Bot should be in at least one guild according to logs")
        except Exception as e:
            self.fail(f"Failed to read bot log: {e}")
    
    def test_02_environment_variables(self):
        """Test if all required environment variables are set"""
        required_vars = [
            'DISCORD_BOT_TOKEN', 
            'GEMINI_API_KEY', 
            'MONGO_URL', 
            'DB_NAME',
            'HELIUS_API_KEY',
            'NFT_CONTRACT_ADDRESS',
            'VERIFIED_CREATOR',
            'IPFS_IMAGE_FOLDER',
            'IPFS_JSON_FOLDER'
        ]
        
        for var in required_vars:
            self.assertIsNotNone(os.getenv(var), f"Environment variable {var} should be set")
            self.assertNotEqual(os.getenv(var), "", f"Environment variable {var} should not be empty")
    
    def test_03_database_setup(self):
        """Test if MongoDB collections are created"""
        expected_collections = [
            'users', 'pets', 'battles', 'tickets', 'ai_knowledge',
            'server_configs', 'leaderboards', 'user_stats'
        ]
        
        actual_collections = self.db.list_collection_names()
        
        for collection in expected_collections:
            self.assertIn(collection, actual_collections, 
                         f"Collection {collection} should exist in the database")
    
    def test_04_ai_knowledge_base(self):
        """Test if AI knowledge base is loaded with initial data"""
        knowledge_entries = list(self.db.ai_knowledge.find({}))
        
        self.assertGreater(len(knowledge_entries), 0, 
                          "AI knowledge base should have at least one entry")
        
        # Check if the initial knowledge about Lil' Gargs is present
        found_initial_knowledge = False
        for entry in knowledge_entries:
            if entry.get('topic') == 'lil_gargs_project':
                found_initial_knowledge = True
                self.assertIn("Lil' Gargs is an NFT collection", entry.get('content', ""), 
                             "Initial knowledge should contain information about Lil' Gargs")
                break
        
        self.assertTrue(found_initial_knowledge, 
                       "Initial knowledge about Lil' Gargs project should be in the database")
    
    def test_05_gemini_api_key_validity(self):
        """Test if the Gemini API key is valid by making a simple request"""
        api_key = os.getenv('GEMINI_API_KEY')
        self.assertIsNotNone(api_key, "Gemini API key should be set")
        
        # Import the emergentintegrations library to test Gemini integration
        try:
            from emergentintegrations.llm.chat import LlmChat, UserMessage
            
            # Create a simple async function to test the API
            async def test_gemini_api():
                try:
                    # Create LLM chat instance
                    chat = LlmChat(
                        api_key=api_key,
                        session_id="test_session",
                        system_message="You are a helpful assistant."
                    )
                    
                    # Configure to use Gemini Flash
                    chat.with_model("gemini", "gemini-2.0-flash")
                    chat.with_max_tokens(100)
                    
                    # Create user message
                    user_message = UserMessage(text="Hello, are you working?")
                    
                    # Get response
                    response = await chat.send_message(user_message)
                    
                    return response
                except Exception as e:
                    logger.error(f"Error testing Gemini API: {e}")
                    return None
            
            # Run the async function
            response = asyncio.run(test_gemini_api())
            
            self.assertIsNotNone(response, "Should get a response from Gemini API")
            self.assertNotEqual(response, "", "Response from Gemini API should not be empty")
            logger.info(f"Gemini API test response: {response[:100]}...")
            
        except ImportError:
            logger.warning("emergentintegrations library not available for testing")
            self.skipTest("emergentintegrations library not available")
    
    def test_06_discord_commands_synced(self):
        """Test if Discord commands are synced according to logs"""
        try:
            with open('/app/backend/bot.log', 'r') as f:
                log_content = f.read()
                
                # Check if commands were synced
                if "Synced" in log_content:
                    import re
                    match = re.search(r"Synced (\d+) command\(s\)", log_content)
                    if match:
                        num_commands = int(match.group(1))
                        self.assertEqual(num_commands, 8, 
                                        f"Expected 8 commands to be synced, but found {num_commands}")
                    else:
                        self.fail("Could not determine number of synced commands from logs")
                else:
                    self.fail("No command sync information found in logs")
        except Exception as e:
            self.fail(f"Failed to read bot log: {e}")

    def test_07_nft_holders_collection(self):
        """Test if nft_holders collection is created with proper indexes"""
        # Check if nft_holders collection exists
        collections = self.db.list_collection_names()
        self.assertIn('nft_holders', collections, 
                     "nft_holders collection should exist in the database")
        
        # Check indexes on nft_holders collection
        indexes = list(self.db.nft_holders.list_indexes())
        index_names = [index['name'] for index in indexes]
        
        self.assertIn('user_id_1', index_names, 
                     "nft_holders collection should have an index on user_id")
        self.assertIn('wallet_address_1', index_names, 
                     "nft_holders collection should have an index on wallet_address")
        
        # Check if user_id index is unique
        for index in indexes:
            if index['name'] == 'user_id_1':
                self.assertTrue(index.get('unique', False), 
                               "user_id index should be unique")
    
    def test_08_helius_api_integration(self):
        """Test the verify_nft_holder method with Helius API"""
        # Test direct API connection instead of using the bot method
        try:
            import aiohttp
            
            # Create async function to test Helius API directly
            async def test_helius_api():
                helius_api_key = os.getenv('HELIUS_API_KEY')
                self.assertIsNotNone(helius_api_key, "Helius API key should be set")
                
                # Test with a real Solana wallet address
                test_wallet = "DYw8jCTfwHNRJhhmFcbXvVDTqWMEVFBX6ZKUmG5CNSKK"
                
                url = f"https://api.helius.xyz/v0/addresses/{test_wallet}/balances"
                params = {
                    "api-key": helius_api_key
                }
                
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params) as response:
                        # Just check if we can connect to the API, don't care about the response
                        status = response.status
                        logger.info(f"Helius API status code: {status}")
                        
                        # Consider 200-299 as success
                        success = 200 <= status < 300
                        
                        if not success:
                            response_text = await response.text()
                            logger.error(f"Helius API error: {status}, Response: {response_text}")
                        
                        return {
                            'success': success,
                            'status': status
                        }
            
            # Run the async function
            result = asyncio.run(test_helius_api())
            
            # We're just testing if the API is accessible, not the specific NFT verification
            self.assertTrue(result['success'], 
                           f"Should be able to connect to Helius API. Got status: {result['status']}")
            
            logger.info(f"Helius API test result: {result}")
            
        except Exception as e:
            logger.error(f"Error testing Helius API integration: {e}")
            self.fail(f"Failed to test Helius API integration: {e}")
    
    def test_09_ipfs_metadata_fetching(self):
        """Test the get_nft_metadata method for fetching NFT metadata from IPFS"""
        try:
            import sys
            sys.path.append('/app/backend')
            from discord_bot import LilGargsBot
            
            # Create async function to test get_nft_metadata
            async def test_get_nft_metadata():
                # Initialize bot
                bot = LilGargsBot()
                
                # Test with a sample token ID
                test_token_id = "1"
                metadata = await bot.get_nft_metadata(test_token_id)
                
                # Check if metadata was fetched successfully
                self.assertIsNotNone(metadata, 
                                   "get_nft_metadata should return metadata for a valid token ID")
                
                # Check if metadata contains expected fields
                if metadata:
                    self.assertIn('name', metadata, 
                                 "Metadata should contain 'name' field")
                    self.assertIn('image', metadata, 
                                 "Metadata should contain 'image' field")
                    self.assertIn('attributes', metadata, 
                                 "Metadata should contain 'attributes' field")
                    self.assertIn('token_id', metadata, 
                                 "Metadata should contain 'token_id' field")
                
                return metadata
            
            # Run the async function
            metadata = asyncio.run(test_get_nft_metadata())
            logger.info(f"IPFS metadata test result: {metadata}")
            
        except ImportError as e:
            logger.error(f"Error importing discord_bot: {e}")
            self.fail(f"Failed to import discord_bot module: {e}")
        except Exception as e:
            logger.error(f"Error testing IPFS metadata fetching: {e}")
            self.fail(f"Failed to test IPFS metadata fetching: {e}")
    
    def test_10_discord_commands_count(self):
        """Test if Discord commands count is updated to 8 (including new NFT command)"""
        try:
            with open('/app/backend/bot.log', 'r') as f:
                log_content = f.read()
                
                # Check if commands were synced
                if "Synced" in log_content:
                    import re
                    match = re.search(r"Synced (\d+) command\(s\)", log_content)
                    if match:
                        num_commands = int(match.group(1))
                        self.assertEqual(num_commands, 8, 
                                        f"Expected 8 commands to be synced (including new NFT command), but found {num_commands}")
                    else:
                        self.fail("Could not determine number of synced commands from logs")
                else:
                    self.fail("No command sync information found in logs")
        except Exception as e:
            self.fail(f"Failed to read bot log: {e}")

if __name__ == '__main__':
    unittest.main()
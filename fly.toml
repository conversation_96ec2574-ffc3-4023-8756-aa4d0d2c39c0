# Fly.io configuration for Lil' Gargs Discord Bot
app = "lil-gargs-discord-bot"
primary_region = "iad"  # US East (Virginia) - change as needed

[build]

[env]
  # Environment variables that are safe to be public
  DB_NAME = "lil_gargs_bot"
  NFT_CONTRACT_ADDRESS = "FP2bGBGHWrW4w82hsSDGc5zNLQ83CvEmW2shGkttS7aZ"
  VERIFIED_CREATOR = "9fT6Spqbv9FxK7Ktxr6bDfASWc6k5acUNr1zMv5WrGfA"
  IPFS_IMAGE_FOLDER = "https://bafybeif32gaqsngxdaply6x5m5htxpuuxw2dljvdv6iokek3xod7lmus24.ipfs.w3s.link/"
  IPFS_JSON_FOLDER = "https://bafybeibelmc3mnauyxzhez3g5wbxmz4n6ibme7hxvkpryqwlj4bha6jeva.ipfs.w3s.link/"

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 1
  processes = ["app"]

  [[http_service.checks]]
    grace_period = "10s"
    interval = "30s"
    method = "GET"
    timeout = "5s"
    path = "/health"

[vm]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 512

[[services]]
  protocol = "tcp"
  internal_port = 8080

  [[services.ports]]
    port = 80
    handlers = ["http"]
    force_https = true

  [[services.ports]]
    port = 443
    handlers = ["tls", "http"]

  [services.concurrency]
    type = "connections"
    hard_limit = 25
    soft_limit = 20

  [[services.tcp_checks]]
    grace_period = "10s"
    interval = "15s"
    restart_limit = 0
    timeout = "2s"

# Restart policy
[restart]
  policy = "always"

# Deploy configuration
[deploy]
  release_command = "echo 'Starting Lil Gargs Discord Bot...'"
  strategy = "immediate"

# Process configuration
[[processes]]
  name = "app"
  cmd = ["python", "discord_bot.py"]

# Secrets (set these using `fly secrets set`)
# DISCORD_BOT_TOKEN=your_discord_bot_token
# GEMINI_API_KEY=your_gemini_api_key  
# HELIUS_API_KEY=your_helius_api_key
# MONGO_URL=your_mongodb_connection_string
# OPENAI_API_KEY=your_openai_api_key (optional)

@echo off
REM Quick Deployment Script for Lil' Gargs Discord Bot on Fly.io (Windows)

echo 🚀 Quick Deploy - Lil' Gargs Discord Bot
echo ========================================

REM Check if flyctl is installed
flyctl version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ flyctl not found. Install it first:
    echo    iwr https://fly.io/install.ps1 -useb ^| iex
    pause
    exit /b 1
)

REM Check if logged in
flyctl auth whoami >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Not logged in to Fly.io. Run:
    echo    flyctl auth login
    pause
    exit /b 1
)

echo ✅ flyctl ready

REM Create app if it doesn't exist
set APP_NAME=lil-gargs-discord-bot
flyctl apps list | findstr "%APP_NAME%" >nul
if %errorlevel% neq 0 (
    echo 📱 Creating app...
    flyctl apps create %APP_NAME%
) else (
    echo ✅ App exists
)

REM Check if .env file exists
if not exist "backend\.env" (
    echo ❌ No backend\.env file found!
    echo Create it with your API keys first.
    pause
    exit /b 1
)

echo 🔑 Setting secrets from .env...

REM Note: You'll need to manually set secrets for now
echo Please run these commands manually:
echo.
echo flyctl secrets set DISCORD_BOT_TOKEN="your_discord_token"
echo flyctl secrets set GEMINI_API_KEY="your_gemini_key"  
echo flyctl secrets set HELIUS_API_KEY="your_helius_key"
echo flyctl secrets set MONGO_URL="your_mongo_url"
echo.
echo Press any key when secrets are set...
pause

REM Deploy
echo 🚀 Deploying...
flyctl deploy

REM Check status
echo 📊 Checking status...
flyctl status

REM Show logs
echo 📋 Recent logs:
flyctl logs --lines 10

echo.
echo 🎉 Deployment complete!
echo.
echo Next steps:
echo 1. Check health: https://%APP_NAME%.fly.dev/health
echo 2. Test in Discord: /systemhealth
echo 3. Monitor logs: flyctl logs --follow
echo.
echo Useful commands:
echo   flyctl logs           - View logs
echo   flyctl restart        - Restart bot
echo   flyctl status         - Check status
echo   flyctl secrets list   - List secrets

pause

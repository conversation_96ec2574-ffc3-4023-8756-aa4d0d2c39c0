# 🚀 Quick Fly.io Deployment Checklist

## ✅ **Pre-Deployment Steps**

### 1. Fix Discord Bot Token
- [ ] Go to [Discord Developer Portal](https://discord.com/developers/applications)
- [ ] Select your bot application
- [ ] Go to "Bot" section → "Reset Token"
- [ ] Copy the new token
- [ ] Update `backend/.env` with new token:
  ```env
  DISCORD_BOT_TOKEN="your_new_token_here"
  ```

### 2. Test Locally (Optional but Recommended)
```bash
cd backend
python discord_bot.py
```
Look for: "✅ Gemini API connection successful" and "<PERSON><PERSON> has connected to Discord!"

### 3. Install Fly.io CLI
```bash
# macOS/Linux
curl -L https://fly.io/install.sh | sh

# Windows
iwr https://fly.io/install.ps1 -useb | iex
```

### 4. Login to Fly.io
```bash
flyctl auth login
```

## 🚀 **Deployment Steps**

### 1. Create Fly.io App
```bash
flyctl apps create lil-gargs-discord-bot
```

### 2. Set Secrets (CRITICAL!)
```bash
# Required secrets - replace with your actual values
flyctl secrets set DISCORD_BOT_TOKEN="your_new_discord_token"
flyctl secrets set GEMINI_API_KEY="AIzaSyCco1Ft8OU8MGL4Y0y3LgglwKfn69OY22A"
flyctl secrets set HELIUS_API_KEY="735ac8d3-af29-4a2b-ad8b-6ed013fe2a04"
flyctl secrets set MONGO_URL="mongodb+srv://lil-gargs-db:<EMAIL>/?retryWrites=true&w=majority&appName=lil-gargs-cluster"
```

### 3. Deploy
```bash
flyctl deploy
```

### 4. Check Status
```bash
flyctl status
flyctl logs
```

### 5. Test Health Check
Visit: https://lil-gargs-discord-bot.fly.dev/health

Expected response:
```json
{
  "status": "healthy",
  "bot_ready": true,
  "ai_enabled": true
}
```

## 🧪 **Testing Your Deployed Bot**

### In Discord:
```
/systemhealth    - Check all services
/askgarg Hello   - Test AI
/nft verify <wallet> - Test NFT verification
/aistats         - Check usage stats
```

## 🚨 **Troubleshooting**

### Bot Won't Start
```bash
flyctl logs
# Look for specific error messages
```

### Common Issues:
1. **Invalid Discord Token**: Get new token from Discord Developer Portal
2. **Missing Secrets**: Run `flyctl secrets list` to verify all are set
3. **Database Issues**: Check MongoDB Atlas whitelist (allow 0.0.0.0/0)
4. **API Key Issues**: Verify Gemini and Helius keys are valid

### Quick Fixes:
```bash
# Restart the app
flyctl restart

# Update a secret
flyctl secrets set DISCORD_BOT_TOKEN="new_token"

# View all secrets
flyctl secrets list

# SSH into app for debugging
flyctl ssh console
```

## 📊 **Monitoring**

### Health Check
- URL: https://lil-gargs-discord-bot.fly.dev/health
- Should return JSON with "status": "healthy"

### Logs
```bash
# Live logs
flyctl logs --follow

# Recent logs
flyctl logs --lines 50

# Error logs only
flyctl logs --search "ERROR"
```

### Performance
```bash
# App status
flyctl status

# Resource usage
flyctl dashboard
```

## 🎯 **Success Indicators**

✅ **Deployment Successful When:**
- `flyctl status` shows "running"
- Health check returns "healthy"
- Discord bot appears online in your server
- `/systemhealth` command works in Discord
- AI commands respond properly

## 🔄 **Updates and Maintenance**

### Deploy Updates:
```bash
git pull  # if using git
flyctl deploy
```

### Scale if Needed:
```bash
flyctl scale count 1  # Ensure 1 instance running
flyctl scale memory 512  # Increase memory if needed
```

### Monitor Costs:
- Check Fly.io dashboard for usage
- Free tier: 3 shared VMs with 256MB RAM
- Estimated cost: ~$2-5/month for small Discord bot

## 📞 **Need Help?**

1. **Check logs first**: `flyctl logs`
2. **Verify secrets**: `flyctl secrets list`
3. **Test health**: Visit health check URL
4. **Discord commands**: Try `/systemhealth` in Discord
5. **Restart if needed**: `flyctl restart`

## 🎉 **You're Done!**

Once all checkboxes are complete and tests pass, your enhanced Lil' Gargs Discord bot is live on Fly.io with:

- ✅ Independent Gemini AI (no emergent.ai dependency)
- ✅ Secure NFT wallet verification
- ✅ Comprehensive rate limiting
- ✅ Administrative tools
- ✅ Health monitoring
- ✅ Production reliability

Enjoy your production Discord bot! 🤖✨
